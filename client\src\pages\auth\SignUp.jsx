import React from "react";
import Logo from "@/components/logos/logo";
import { Link } from "react-router-dom";
import { SignUpForm } from "@/components/forms/auth/sign-up-form";

export default function SignUp({
  logo = <Logo />,
  logoTitle = "School Track",
  logoHref = "/",
  title = "Create your account",
  description = "Enter your details below to create your account",
}) {
  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <Link
            to={logoHref}
            className="flex items-center gap-2 text-xl font-bold"
          >
            {logo}
            {logoTitle}
          </Link>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-md">
            <div className="w-full max-w-sm mx-auto">
              <div className="space-y-2 mb-6">
                <h1 className="text-3xl font-semibold tracking-tight">
                  {title}
                </h1>
                <p className="text-base text-muted-foreground">{description}</p>
              </div>
              <SignUpForm />
            </div>
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block"></div>
    </div>
  );
}
