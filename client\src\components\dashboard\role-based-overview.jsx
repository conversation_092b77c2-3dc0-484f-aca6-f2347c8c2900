import React from "react";
import { useAuth } from "@/context/auth-context";
import { Skeleton } from "@/components/ui/skeleton";

// Import role-specific overview components
import AdminOverview from "@/pages/dashboard/admin/Overview";
import SchoolAdminOverview from "@/pages/dashboard/school-admin/Overview";
import TeacherOverview from "@/pages/dashboard/teacher/Overview";
import StudentOverview from "@/pages/dashboard/student/Overview";
import ParentOverview from "@/pages/dashboard/parent/Overview";

const RoleBasedOverview = () => {
  const { user, isLoading } = useAuth();

  if (isLoading || !user) {
    return (
      <div>
        <Skeleton />
      </div>
    );
  }

  switch (user?.role) {
    case "admin":
      return <AdminOverview />;
    case "school-admin":
      return <SchoolAdminOverview />;
    case "teacher":
      return <TeacherOverview />;
    case "student":
      return <StudentOverview />;
    case "parent":
      return <ParentOverview />;
    default:
      return <StudentOverview />;
  }
};

export default RoleBasedOverview;
