import axiosInstance from "./axios-instance";

export const submitContactForm = async (formData) => {
  try {
    const response = await axiosInstance.post("/contact/create", formData);
    return response.data;
  } catch (error) {
    console.error(
      "Error submitting contact form:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to submit contact form");
  }
};

export const getAllContactSubmissions = async () => {
  try {
    const response = await axiosInstance.get("/contact");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching contact submissions:",
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error("Failed to fetch contact submissions")
    );
  }
};

export const getContactSubmissionById = async (id) => {
  try {
    const response = await axiosInstance.get(`/contact/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching contact submission ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch contact submission ${id}`)
    );
  }
};

export const updateContactSubmissionStatus = async (id, statusUpdate) => {
  try {
    const response = await axiosInstance.put(
      `/contact/${id}/status`,
      statusUpdate
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error updating status for contact submission ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update status for contact submission ${id}`)
    );
  }
};
