import React from "react";
import { NavMain } from "@/components/sidebar/nav-main";
import { NavProjects } from "@/components/sidebar/nav-config";
import { NavUser } from "@/components/sidebar/nav-user";
import { SidebarLogo } from "@/components/sidebar/sidebar-logo";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { useAuth } from "@/context/auth-context";
import { navigationData, defaultNavigation } from "./sidebar-config";

export function AppSidebar({ ...props }) {
  const { user } = useAuth();
  const role = user?.role || "student";
  const navItems = navigationData[role] || defaultNavigation;

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarLogo />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={navItems.navMain} />
        <NavProjects configurations={navItems.configurations} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
