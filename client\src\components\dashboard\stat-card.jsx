import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export function StatCard({
  title,
  value,
  description,
  icon,
  isLoading = false,
  className = "",
  trend = null,
}) {
  const Icon = icon;

  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs">
      <Card className={`@container/card ${className}`}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">
            {isLoading ? <Skeleton className="h-4 w-24" /> : title}
          </CardTitle>
          {Icon && (
            <div className="text-muted-foreground">
              {isLoading ? (
                <Skeleton className="h-4 w-4 rounded-full" />
              ) : (
                <Icon className="h-4 w-4" />
              )}
            </div>
          )}
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="space-y-2">
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-4 w-28" />
            </div>
          ) : (
            <>
              <div className="text-2xl font-bold capitalize">{value}</div>
              {description && (
                <p
                  className={`text-xs ${
                    trend === "positive"
                      ? "text-success"
                      : trend === "negative"
                      ? "text-destructive"
                      : trend === "warning"
                      ? "text-warning"
                      : "text-muted-foreground"
                  }`}
                >
                  {description}
                </p>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
