import { useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogD<PERSON><PERSON>,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import {
  Plus,
  Building,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  FileText,
  Loader2,
} from "lucide-react";

const departmentTypes = [
  { value: "academic", label: "Academic" },
  { value: "administrative", label: "Administrative" },
  { value: "support", label: "Support" },
  { value: "research", label: "Research" },
];

const statusOptions = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

export default function CreateDepartmentDialog({ onDepartmentCreated }) {
  const [open, setOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm({
    defaultValues: {
      name: "",
      type: "",
      head: "",
      headTitle: "",
      headEmail: "",
      headPhone: "",
      email: "",
      phone: "",
      building: "",
      floor: "",
      establishedYear: "",
      description: "",
      status: "active",
    },
  });

  const handleSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Create new department object with additional fields
      const newDepartment = {
        id: Date.now(),
        ...data,
        totalTeachers: 0,
        totalStudents: 0,
        performance: 85,
        courses: [],
        faculty: [],
        facilities: [],
      };

      // Call the callback to add the department to the list
      if (onDepartmentCreated) {
        onDepartmentCreated(newDepartment);
      }

      toast.success("Department created successfully!", {
        description: `${data.name} has been added to the department list.`,
      });

      form.reset();
      setOpen(false);
    } catch (error) {
      console.error("Error creating department:", error);
      toast.error("Failed to create department", {
        description: "Please try again later.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="gap-2">
          <Plus className="h-4 w-4" />
          Add Department
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building className="h-5 w-5" />
            Create New Department
          </DialogTitle>
          <DialogDescription>
            Add a new department to your school. Fill in the required
            information below.
          </DialogDescription>
        </DialogHeader>

        <ScrollArea className="max-h-[60vh] pr-4">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(handleSubmit)}
              className="space-y-6"
            >
              {/* Basic Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <Building className="h-4 w-4" />
                  Basic Information
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="name"
                    label="Department Name"
                    placeholder="e.g., Computer Science"
                    validation={{ required: "Department name is required" }}
                  />
                  <SelectInput
                    form={form}
                    name="type"
                    label="Department Type"
                    placeholder="Select type"
                    options={departmentTypes}
                    validation={{ required: "Department type is required" }}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <DateInput
                    form={form}
                    name="establishedYear"
                    label="Established Year"
                    placeholder="YYYY-MM-DD"
                    validation={{ required: "Established year is required" }}
                  />
                  <SelectInput
                    form={form}
                    name="status"
                    label="Status"
                    placeholder="Select status"
                    options={statusOptions}
                    validation={{ required: "Status is required" }}
                  />
                </div>
                <TextareaInput
                  form={form}
                  name="description"
                  label="Description"
                  placeholder="Brief description of the department..."
                  validation={{ required: "Description is required" }}
                />
              </div>

              <Separator />

              {/* Department Head Information */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <User className="h-4 w-4" />
                  Department Head
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="head"
                    label="Head Name"
                    placeholder="Dr. John Smith"
                    validation={{
                      required: "Department head name is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="headTitle"
                    label="Title"
                    placeholder="Professor & Department Head"
                    validation={{ required: "Head title is required" }}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="headEmail"
                    label="Email"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="headPhone"
                    label="Phone Number"
                    validation={{ required: "Phone number is required" }}
                  />
                </div>
              </div>

              <Separator />

              {/* Contact & Location */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <MapPin className="h-4 w-4" />
                  Contact & Location
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="email"
                    label="Department Email"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Department email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="phone"
                    label="Department Phone"
                    validation={{ required: "Department phone is required" }}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="building"
                    label="Building"
                    placeholder="Science Building"
                    validation={{ required: "Building is required" }}
                  />
                  <TextInput
                    form={form}
                    name="floor"
                    label="Floor/Room Details"
                    placeholder="3rd Floor, Rooms 301-320"
                    validation={{ required: "Floor/room details are required" }}
                  />
                </div>
              </div>
            </form>
          </Form>
        </ScrollArea>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => setOpen(false)}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            onClick={form.handleSubmit(handleSubmit)}
            disabled={isSubmitting}
            className="gap-2"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              <>
                <Plus className="h-4 w-4" />
                Create Department
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
