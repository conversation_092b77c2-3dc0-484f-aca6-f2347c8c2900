import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export const InfoCard = ({
  icon: Icon,
  title,
  children,
  className = "",
  isLoading = false,
}) => (
  <Card className={`shadow-sm hover:shadow-md transition-shadow ${className}`}>
    <CardHeader>
      <div className="flex items-center space-x-2">
        {isLoading ? (
          <>
            <Skeleton className="h-8 w-8 rounded-lg" />
            <Skeleton className="h-4 w-32" />
          </>
        ) : (
          <>
            <div className="p-2 bg-primary/10 rounded-lg">
              <Icon className="h-4 w-4 text-primary" />
            </div>
            <h3 className="font-semibold text-foreground">{title}</h3>
          </>
        )}
      </div>
    </CardHeader>
    <Separator />
    <CardContent>
      {isLoading ? (
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      ) : (
        children
      )}
    </CardContent>
  </Card>
);
