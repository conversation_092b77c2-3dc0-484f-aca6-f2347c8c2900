import { Card, CardContent, CardHeader } from "@/components/ui/card";

export const InfoCard = ({ icon: Icon, title, children, className = "" }) => (
  <Card className={`shadow-sm hover:shadow-md transition-shadow ${className}`}>
    <CardHeader>
      <div className="flex items-center space-x-2">
        <div className="p-2 bg-primary/10 rounded-lg">
          <Icon className="h-4 w-4 text-primary" />
        </div>
        <h3 className="font-semibold text-foreground">{title}</h3>
      </div>
    </CardHeader>
    <CardContent className="pt-0">{children}</CardContent>
  </Card>
);
