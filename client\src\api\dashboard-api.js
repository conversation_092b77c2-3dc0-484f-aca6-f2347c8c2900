import axiosInstance from "./axios-instance";

// Get admin dashboard statistics
export const getAdminDashboardStats = async () => {
  try {
    const response = await axiosInstance.get("/dashboard/admin/stats");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching admin dashboard stats:",
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error("Failed to fetch dashboard statistics")
    );
  }
};

// Get school admin dashboard statistics
export const getSchoolAdminDashboardStats = async () => {
  try {
    const response = await axiosInstance.get("/dashboard/school-admin/stats");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching school admin dashboard stats:",
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error("Failed to fetch dashboard statistics")
    );
  }
};

// Get admin chart data for schools and school admins creation
export const getAdminChartData = async (timeRange = "30d") => {
  try {
    const response = await axiosInstance.get(
      `/dashboard/admin/chart-data?timeRange=${timeRange}`
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching admin chart data:",
      error.response?.data || error.message
    );
    throw error.response?.data || error;
  }
};
