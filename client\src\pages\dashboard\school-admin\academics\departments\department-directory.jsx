import { useState, useEffect } from "react";
import { Menu } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  Sheet<PERSON>eader,
  SheetTitle,
  SheetDescription,
  SheetTrigger,
} from "@/components/ui/sheet";
import DepartmentList from "./DepartmentList";
import DepartmentDetails from "./DepartmentDetails";
import CreateDepartmentDialog from "./create-department-dialog";
import { toast } from "sonner";

// Dummy data for departments
const dummyDepartments = [
  {
    id: 1,
    name: "Computer Science",
    head: "Dr. <PERSON>",
    headTitle: "Professor & Department Head",
    headEmail: "<EMAIL>",
    headPhone: "+****************",
    totalTeachers: 15,
    totalStudents: 245,
    status: "active",
    type: "Academic",
    establishedYear: 1995,
    building: "Technology Building",
    floor: "3rd Floor, Rooms 301-320",
    email: "<EMAIL>",
    phone: "+****************",
    performance: 92,
    description:
      "The Computer Science Department offers comprehensive programs in software engineering, artificial intelligence, cybersecurity, and data science. Our faculty are renowned researchers and industry experts committed to providing students with cutting-edge knowledge and practical skills.",
    courses: [
      {
        name: "Bachelor of Computer Science",
        code: "BCS",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Master of Computer Science",
        code: "MCS",
        duration: "2 years",
        level: "Graduate",
      },
      {
        name: "Software Engineering",
        code: "SE",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Data Science",
        code: "DS",
        duration: "2 years",
        level: "Graduate",
      },
      {
        name: "Cybersecurity",
        code: "CYB",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Artificial Intelligence",
        code: "AI",
        duration: "2 years",
        level: "Graduate",
      },
    ],
    faculty: [
      {
        name: "Dr. Sarah Johnson",
        position: "Professor",
        specialization: "Artificial Intelligence",
      },
      {
        name: "Dr. Michael Chen",
        position: "Associate Professor",
        specialization: "Software Engineering",
      },
      {
        name: "Dr. Emily Rodriguez",
        position: "Assistant Professor",
        specialization: "Cybersecurity",
      },
      {
        name: "Dr. James Wilson",
        position: "Professor",
        specialization: "Data Science",
      },
      {
        name: "Dr. Lisa Thompson",
        position: "Associate Professor",
        specialization: "Machine Learning",
      },
      {
        name: "Dr. Robert Kim",
        position: "Assistant Professor",
        specialization: "Web Development",
      },
    ],
    facilities: [
      {
        name: "Computer Lab A",
        description: "High-performance computing lab with 30 workstations",
        capacity: "30 students",
      },
      {
        name: "AI Research Lab",
        description: "Specialized lab for artificial intelligence research",
        capacity: "15 researchers",
      },
      {
        name: "Software Development Studio",
        description: "Collaborative space for software projects",
        capacity: "25 students",
      },
      {
        name: "Cybersecurity Lab",
        description: "Secure environment for cybersecurity training",
        capacity: "20 students",
      },
    ],
  },
  {
    id: 2,
    name: "Mathematics",
    head: "Dr. Robert Anderson",
    headTitle: "Professor & Department Head",
    headEmail: "<EMAIL>",
    headPhone: "+****************",
    totalTeachers: 12,
    totalStudents: 180,
    status: "active",
    type: "Academic",
    establishedYear: 1985,
    building: "Science Building",
    floor: "2nd Floor, Rooms 201-215",
    email: "<EMAIL>",
    phone: "+****************",
    performance: 88,
    description:
      "The Mathematics Department provides a strong foundation in pure and applied mathematics, statistics, and mathematical modeling. Our programs prepare students for careers in education, research, finance, and technology.",
    courses: [
      {
        name: "Bachelor of Mathematics",
        code: "BMATH",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Master of Mathematics",
        code: "MMATH",
        duration: "2 years",
        level: "Graduate",
      },
      {
        name: "Applied Mathematics",
        code: "AMATH",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Statistics",
        code: "STAT",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Mathematical Finance",
        code: "MFIN",
        duration: "2 years",
        level: "Graduate",
      },
    ],
    faculty: [
      {
        name: "Dr. Robert Anderson",
        position: "Professor",
        specialization: "Pure Mathematics",
      },
      {
        name: "Dr. Maria Garcia",
        position: "Associate Professor",
        specialization: "Statistics",
      },
      {
        name: "Dr. David Lee",
        position: "Assistant Professor",
        specialization: "Applied Mathematics",
      },
      {
        name: "Dr. Jennifer Brown",
        position: "Professor",
        specialization: "Mathematical Modeling",
      },
      {
        name: "Dr. Thomas White",
        position: "Associate Professor",
        specialization: "Calculus",
      },
    ],
    facilities: [
      {
        name: "Mathematics Learning Center",
        description: "Tutoring and study space for mathematics students",
        capacity: "40 students",
      },
      {
        name: "Statistics Lab",
        description: "Computer lab for statistical analysis",
        capacity: "25 students",
      },
      {
        name: "Research Library",
        description: "Specialized mathematics research collection",
        capacity: "20 researchers",
      },
    ],
  },
  {
    id: 3,
    name: "Physics",
    head: "Dr. Amanda Taylor",
    headTitle: "Professor & Department Head",
    headEmail: "<EMAIL>",
    headPhone: "+****************",
    totalTeachers: 10,
    totalStudents: 120,
    status: "active",
    type: "Academic",
    establishedYear: 1990,
    building: "Science Building",
    floor: "1st Floor, Rooms 101-115",
    email: "<EMAIL>",
    phone: "+****************",
    performance: 90,
    description:
      "The Physics Department offers comprehensive programs in theoretical and experimental physics, preparing students for careers in research, engineering, and technology. Our state-of-the-art laboratories provide hands-on experience with modern equipment.",
    courses: [
      {
        name: "Bachelor of Physics",
        code: "BPHYS",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Master of Physics",
        code: "MPHYS",
        duration: "2 years",
        level: "Graduate",
      },
      {
        name: "Applied Physics",
        code: "APHYS",
        duration: "4 years",
        level: "Undergraduate",
      },
      {
        name: "Quantum Physics",
        code: "QPHYS",
        duration: "2 years",
        level: "Graduate",
      },
      {
        name: "Astrophysics",
        code: "ASTRO",
        duration: "4 years",
        level: "Undergraduate",
      },
    ],
    faculty: [
      {
        name: "Dr. Amanda Taylor",
        position: "Professor",
        specialization: "Quantum Mechanics",
      },
      {
        name: "Dr. Christopher Davis",
        position: "Associate Professor",
        specialization: "Astrophysics",
      },
      {
        name: "Dr. Rachel Green",
        position: "Assistant Professor",
        specialization: "Particle Physics",
      },
      {
        name: "Dr. Kevin Martinez",
        position: "Professor",
        specialization: "Condensed Matter",
      },
      {
        name: "Dr. Susan Clark",
        position: "Associate Professor",
        specialization: "Optics",
      },
    ],
    facilities: [
      {
        name: "Quantum Physics Lab",
        description: "Advanced laboratory for quantum experiments",
        capacity: "15 students",
      },
      {
        name: "Optics Laboratory",
        description: "Specialized lab for optical physics experiments",
        capacity: "20 students",
      },
      {
        name: "Observatory",
        description: "On-campus observatory for astronomical observations",
        capacity: "10 observers",
      },
      {
        name: "Particle Accelerator Lab",
        description: "Small-scale particle physics experiments",
        capacity: "12 students",
      },
    ],
  },
];

export default function DepartmentManagement() {
  const [departments, setDepartments] = useState([]);
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  useEffect(() => {
    // Simulate API call with dummy data
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // Simulate network delay
        await new Promise((resolve) => setTimeout(resolve, 1000));

        setDepartments(dummyDepartments);
        if (dummyDepartments.length > 0) {
          setSelectedDepartment(dummyDepartments[0]);
        }

        toast.success("Departments loaded successfully");
      } catch (error) {
        console.error("Error fetching departments:", error);
        toast.error("Error fetching departments", {
          description: error.message || "Please try again later.",
        });
        setDepartments([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const filteredDepartments = departments.filter(
    (dept) =>
      dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dept.head.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectDepartment = (department) => {
    setSelectedDepartment(department);
    setIsSheetOpen(false);
  };

  const handleDepartmentCreated = (newDepartment) => {
    setDepartments((prevDepartments) => [...prevDepartments, newDepartment]);
    setSelectedDepartment(newDepartment);
    toast.success("Department added successfully!", {
      description: `${newDepartment.name} is now available in the department list.`,
    });
  };

  return (
    <div className="flex h-full w-full relative">
      {/* Mobile Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetTrigger asChild>
          <Button
            size="icon"
            className="md:hidden absolute top-6 right-4 z-50"
            aria-label="Open menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 p-4">
          <SheetHeader>
            <SheetTitle>Department Navigation</SheetTitle>
            <SheetDescription>Browse and manage departments</SheetDescription>
          </SheetHeader>
          <DepartmentList
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            departments={filteredDepartments}
            selectedDepartment={selectedDepartment}
            onSelect={handleSelectDepartment}
            isLoading={isLoading}
            isMobile
            createDepartmentDialog={
              <CreateDepartmentDialog
                onDepartmentCreated={handleDepartmentCreated}
              />
            }
          />
        </SheetContent>
      </Sheet>

      {/* Sidebar (Desktop) */}
      <DepartmentList
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        departments={filteredDepartments}
        selectedDepartment={selectedDepartment}
        onSelect={handleSelectDepartment}
        isLoading={isLoading}
        createDepartmentDialog={
          <CreateDepartmentDialog
            onDepartmentCreated={handleDepartmentCreated}
          />
        }
      />

      {/* Main Content */}
      <div className="flex-1 h-full overflow-hidden">
        <DepartmentDetails
          selectedDepartment={selectedDepartment}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
