import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { DepartmentForm } from "@/components/forms/dashboard/departments/department-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { toast } from "sonner";

const CreateDepartments = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [departmentData, setDepartmentData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadDepartmentData = async () => {
      if (id) {
        try {
          // Simulate API call to fetch department data
          await new Promise((resolve) => setTimeout(resolve, 1000));
          
          // Mock department data for editing
          const mockData = {
            name: "Computer Science",
            type: "academic",
            establishedYear: "2010-01-01",
            status: "active",
            description: "Department of Computer Science and Engineering",
            head: "<PERSON>. <PERSON>",
            headTitle: "Professor & Department Head",
            headEmail: "<EMAIL>",
            headPhone: "+91 9876543210",
            email: "<EMAIL>",
            phone: "+91 9876543211",
            building: "Science Building",
            floor: "3rd Floor, Rooms 301-320",
          };
          
          setDepartmentData(mockData);
        } catch (error) {
          console.error("Failed to fetch department data:", error);
          toast.error("Failed to load department data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadDepartmentData();
  }, [id, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Department" : "Create New Department"}
        actions={[
          {
            label: "Back to Departments",
            href: "/dashboard/academics/departments",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Academics", href: "/dashboard/academics" },
          { label: "Departments", href: "/dashboard/academics/departments" },
          { label: id ? "Edit Department" : "Create Department" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <DepartmentForm editingId={id} initialData={departmentData} />
      )}
    </Container>
  );
};

export default CreateDepartments;
