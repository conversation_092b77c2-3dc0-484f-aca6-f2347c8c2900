import { useEffect } from "react";
import { useSchool } from "@/context/school-context";
import {
  GraduationCap,
  MapPin,
  School,
  PlusCircle,
  BookOpen,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { SchoolColumns } from "@/pages/dashboard/admin/schools/school-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const SchoolDirectory = () => {
  const { schools, isLoading, fetchAllSchools } = useSchool();

  useEffect(() => {
    fetchAllSchools();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="School Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Schools" },
          ]}
          actions={[
            {
              label: "New School",
              icon: PlusCircle,
              href: "/dashboard/schools/create",
            },
          ]}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Schools"
            value={schools.length}
            description="All registered schools"
            icon={School}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Schools"
            value={
              schools.filter((school) => school.status === "active").length
            }
            description="Currently operational"
            icon={GraduationCap}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Recent Additions"
            value={
              schools.filter((school) => {
                const createdAt = new Date(school.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Added in last 30 days"
            icon={BookOpen}
            isLoading={isLoading}
          />

          <StatCard
            title="Top Country"
            value={(() => {
              if (schools.length === 0) return "N/A";
              const countryCounts = schools.reduce((acc, school) => {
                const country = school.country || "Unknown";
                acc[country] = (acc[country] || 0) + 1;
                return acc;
              }, {});
              let topCountry = "Unknown";
              let maxCount = 0;
              for (const [country, count] of Object.entries(countryCounts)) {
                if (count > maxCount) {
                  maxCount = count;
                  topCountry = country;
                }
              }
              return topCountry;
            })()}
            description="Most schools in country"
            icon={MapPin}
            isLoading={isLoading}
          />
        </div>
        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={schools}
              columns={SchoolColumns()}
              model="school"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default SchoolDirectory;
