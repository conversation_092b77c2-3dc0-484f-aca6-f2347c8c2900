import { Badge } from "@/components/ui/badge";

export function StatusColumn({
  row,
  statusField = "status",
  variant = {
    default: "default",
    secondary: "secondary",
    warning: "warning",
    success: "success",
    outline: "outline",
    destructive: "destructive",
  },
}) {
  const status = row.original[statusField];

  const badgeVariant = variant[status] || "outline";

  return (
    <Badge variant={badgeVariant} className="capitalize">
      {status}
    </Badge>
  );
}
