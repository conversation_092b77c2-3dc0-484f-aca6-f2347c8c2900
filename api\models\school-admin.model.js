import mongoose from "mongoose";

const SchoolAdminSchema = new mongoose.Schema(
  {
    // Personal Information
    firstName: {
      type: String,
      required: [true, "First name is required"],
      trim: true,
    },
    lastName: {
      type: String,
      required: [true, "Last name is required"],
      trim: true,
    },
    phone: {
      type: String,
      required: [true, "Phone number is required"],
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    gender: {
      type: String,
      required: [true, "Gender is required"],
      enum: ["Male", "Female", "Other"],
      trim: true,
    },
    dateOfBirth: {
      type: Date,
      required: [true, "Date of birth is required"],
    },
    bloodGroup: {
      type: String,
      trim: true,
    },
    maritalStatus: {
      type: String,
      enum: ["Single", "Married", "Divorced"],
      trim: true,
    },
    nationality: {
      type: String,
      required: [true, "Nationality is required"],
      trim: true,
    },
    religion: {
      type: String,
      trim: true,
    },
    aadharNumber: {
      type: String,
      trim: true,
      match: [/^\d{12}$/, "Aadhar number must be 12 digits"],
    },
    panNumber: {
      type: String,
      trim: true,
      match: [/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, "Invalid PAN number format"],
    },

    // Address Information
    currentAddress: {
      type: String,
      required: [true, "Current address is required"],
      trim: true,
    },
    permanentAddress: {
      type: String,
      required: [true, "Permanent address is required"],
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    state: {
      type: String,
      required: [true, "State is required"],
      trim: true,
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      trim: true,
    },
    postalCode: {
      type: String,
      required: [true, "Postal code is required"],
      trim: true,
    },

    // Administrative Details
    adminId: {
      type: String,
      required: [true, "Admin ID is required"],
      trim: true,
      unique: true,
    },
    role: {
      type: String,
      required: [true, "Role is required"],
      trim: true,
    },
    department: {
      type: String,
      required: [true, "Department is required"],
      trim: true,
    },
    joiningDate: {
      type: Date,
      required: [true, "Joining date is required"],
    },
    employmentType: {
      type: String,
      enum: ["Full Time", "Part Time", "Contract", "Permanent"],
      trim: true,
    },

    // Professional Information
    qualification: {
      type: String,
      trim: true,
    },
    experience: {
      type: String,
      trim: true,
    },
    designation: {
      type: String,
      trim: true,
    },
    previousEmployer: {
      type: String,
      trim: true,
    },
    specialization: {
      type: String,
      trim: true,
    },

    // Emergency Contact
    emergencyContactName: {
      type: String,
      trim: true,
    },
    emergencyContactRelationship: {
      type: String,
      trim: true,
    },
    emergencyContactPhone: {
      type: String,
      trim: true,
    },
    emergencyContactEmail: {
      type: String,
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    emergencyContactAddress: {
      type: String,
      trim: true,
    },

    // Documents & Media
    profilePhoto: {
      type: String,
      trim: true,
    },
    identityProof: {
      type: String,
      trim: true,
    },
    resume: {
      type: String,
      trim: true,
    },
    educationalCertificates: {
      type: String,
      trim: true,
    },

    // System Access & Permissions
    schoolId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "School",
      required: [true, "School ID is required"],
    },
    schoolName: {
      type: String,
      required: [true, "School name is required"],
      trim: true,
    },
    username: {
      type: String,
      required: [true, "Username is required"],
      trim: true,
      unique: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      unique: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Permissions
    canManageStudents: {
      type: Boolean,
      default: false,
    },
    canManageTeachers: {
      type: Boolean,
      default: false,
    },
    canManageStaff: {
      type: Boolean,
      default: false,
    },
    canManageClasses: {
      type: Boolean,
      default: false,
    },
    canManageSubjects: {
      type: Boolean,
      default: false,
    },
    canManageExams: {
      type: Boolean,
      default: false,
    },
    canManageAttendance: {
      type: Boolean,
      default: false,
    },
    canManageFees: {
      type: Boolean,
      default: false,
    },
    canManageReports: {
      type: Boolean,
      default: false,
    },
    canManageSettings: {
      type: Boolean,
      default: false,
    },
    canManageAdmissions: {
      type: Boolean,
      default: false,
    },
    canManageEvents: {
      type: Boolean,
      default: false,
    },

    // Additional Information
    bio: {
      type: String,
      trim: true,
    },
    skills: {
      type: String,
      trim: true,
    },
    languages: {
      type: String,
      trim: true,
    },
    hobbies: {
      type: String,
      trim: true,
    },
    achievements: {
      type: String,
      trim: true,
    },
    notes: {
      type: String,
      trim: true,
    },

    // Status
    status: {
      type: String,
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("SchoolAdmin", SchoolAdminSchema);
