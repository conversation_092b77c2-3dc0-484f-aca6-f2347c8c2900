import axiosInstance from "./axios-instance";

export const createParent = async (parentData) => {
  try {
    const response = await axiosInstance.post("/parents/create", parentData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating parent:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create parent");
  }
};

export const getParents = async () => {
  try {
    const response = await axiosInstance.get("/parents");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching parents:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch parents");
  }
};

export const getParentById = async (id) => {
  try {
    const response = await axiosInstance.get(`/parents/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching parent with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch parent with ID ${id}`)
    );
  }
};

export const updateParent = async (id, parentData) => {
  try {
    const response = await axiosInstance.put(`/parents/${id}`, parentData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating parent with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update parent with ID ${id}`)
    );
  }
};

export const deleteParent = async (id) => {
  try {
    const response = await axiosInstance.delete(`/parents/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting parent with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete parent with ID ${id}`)
    );
  }
};

export const updateParentStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/parents/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating parent status for parent with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update parent status for parent with ID ${id}`)
    );
  }
};
