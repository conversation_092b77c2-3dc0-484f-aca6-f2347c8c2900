import { createContext, useContext, useState, useEffect } from "react";
import {
  signin as apiLogin,
  signup as apiRegister,
  logout as apiLogout,
  getProfile,
} from "@/api/auth-api";

const AuthContext = createContext({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  error: null,
  login: () => {},
  logout: () => {},
  register: () => {},
});

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem("token");
      if (token) {
        try {
          const userData = await getProfile();
          setUser(userData.user);
          setError(null);
        } catch (error) {
          console.error("Authentication check failed:", error);
          logout();
          setError("Session expired. Please login again.");
        }
      }
      setIsLoading(false);
    };
    checkAuth();
  }, []);

  const login = async (credentials) => {
    setError(null);
    try {
      const { token, user } = await apiLogin(credentials);
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(user));
      setUser(user);
      return true;
    } catch (error) {
      setError(error.message || "Login failed");
      throw error;
    }
  };

  const logout = async () => {
    try {
      await apiLogout();
    } catch (error) {
      console.error("Logout API call failed:", error);
    } finally {
      localStorage.removeItem("token");
      localStorage.removeItem("user");
      setUser(null);
      setError(null);
    }
  };

  const register = async (userData) => {
    setError(null);
    try {
      const { token, user } = await apiRegister(userData);
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(user));
      setUser(user);
      return true;
    } catch (error) {
      setError(error.message || "Registration failed");
      throw error;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated: !!user,
        error,
        login,
        logout,
        register,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
