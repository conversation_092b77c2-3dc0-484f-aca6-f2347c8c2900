import Figma from "@/components/logos/figma";
import React from "@/components/logos/react";
import ShadcnUi from "@/components/logos/shadcn-ui";
import Tailwind from "@/components/logos/tailwind";
import JavaScript from "@/components/logos/javascript";
import Logo from "@/components/ui/logo";
import { Section } from "@/components/ui/section";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";

export default function TechStack({
  title = "Built with Modern Technology for Educational Excellence",
  subtitle = "Our platform leverages cutting-edge technologies to deliver a reliable, secure, and user-friendly experience",
  badge = (
    <Announcement>
      <AnnouncementTag>TECHNOLOGY</AnnouncementTag>
      <AnnouncementTitle>
        Continuously updated for optimal performance
      </AnnouncementTitle>
    </Announcement>
  ),
  logos = [
    <Logo key="figma" image={Figma} name="Figma" />,
    <Logo key="react" image={React} name="React" version="19.0.0" />,
    <Logo
      key="javascript"
      image={JavaScript}
      name="JavaScript"
      version="5.6.3"
    />,
    <Logo
      key="shadcn"
      image={ShadcnUi}
      name="Shadcn/ui"
      version="2.4.0"
      badge="New"
    />,
    <Logo
      key="tailwind"
      image={Tailwind}
      name="Tailwind"
      version="4.0"
      badge="New"
    />,
  ],
}) {
  return (
    <Section className="group relative overflow-hidden">
      <Container className="flex flex-col items-center gap-8 text-center">
        {badge !== false && badge}
        <h2 className="text-md font-semibold sm:text-2xl">
          {title}
          {subtitle && (
            <p className="mt-4 text-lg leading-relaxed text-muted-foreground sm:text-xl">
              {subtitle}
            </p>
          )}
        </h2>
        {logos !== false && logos.length > 0 && (
          <div className="flex flex-wrap items-center justify-center gap-8">
            {logos}
          </div>
        )}
      </Container>
    </Section>
  );
}
