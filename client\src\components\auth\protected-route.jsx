import { useAuth } from "@/context/auth-context";
import { Navigate, useLocation } from "react-router-dom";

export const ProtectedRoute = ({
  children,
  allowedRoles = [],
  redirectPath = "/sign-in",
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return <></>;
  }

  if (!isAuthenticated) {
    return <Navigate to={redirectPath} state={{ from: location }} replace />;
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    return <Navigate to="/dashboard" replace />;
  }

  return children;
};
