import { useState } from "react";
import { AlertT<PERSON>gle, Calendar, <PERSON>, Bell } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { formatTimeAgo } from "@/utils/date-filters";
import { StatusBadge } from "@/components/data-card/data-card-component/status-badge";
import { DropdownActions } from "@/components/data-card/data-card-component/dropdown-actions";

export const NotificationCard = ({
  notification,
  onEdit,
  onView,
  onDelete,
  onSend,
}) => {
  const [openDelete, setOpenDelete] = useState(false);
  const [openSend, setOpenSend] = useState(false);

  const getNotificationIcon = (type) => {
    switch (type) {
      case "Emergency":
        return <AlertTriangle className="h-4 w-4 text-destructive" />;
      case "Event":
        return <Calendar className="h-4 w-4 text-success" />;
      case "Academic":
        return <Clock className="h-4 w-4 text-warning" />;
      default:
        return <Bell className="h-4 w-4 text-primary" />;
    }
  };

  const handleDelete = async () => {
    try {
      await onDelete(notification._id);
      toast.success(`${notification.title} deleted successfully.`);
      setOpenDelete(false);
    } catch (error) {
      console.error("Failed to delete notification:", error);
      toast.error("Failed to delete notification. Please try again.");
    }
  };

  const handleSend = async () => {
    try {
      if (notification.status === "sent") {
        toast.error("Notification has already been sent");
        setOpenSend(false);
        return;
      }
      await onSend(notification._id);
      toast.success(`${notification.title} sent successfully.`);
      setOpenSend(false);
    } catch (error) {
      console.error("Failed to send notification:", error);
      toast.error("Failed to send notification. Please try again.");
    }
  };

  return (
    <>
      <Card
        className={cn(
          "transition-all duration-200 hover:shadow-md cursor-pointer",
          notification.priority === "High" &&
            "border-l-4 border-l-primary bg-accent/30"
        )}
      >
        <CardContent className="px-4">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <div className="flex-shrink-0 mt-1">
                {getNotificationIcon(notification.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between mb-1">
                  <h3 className="text-sl font-semibold text-foreground line-clamp-1">
                    {notification.title}
                  </h3>
                  <div className="flex items-center space-x-2">
                    <StatusBadge
                      row={{ original: notification }}
                      statusField="priority"
                      variant={{
                        Low: "success",
                        High: "destructive",
                        Medium: "warning",
                      }}
                    />
                    <StatusBadge
                      row={{ original: notification }}
                      showText={false}
                      statusField="status"
                      variant={{
                        draft: "secondary",
                        scheduled: "warning",
                        sent: "success",
                        failed: "destructive",
                      }}
                    />
                  </div>
                </div>
                <div className="flex items-center text-xs text-muted-foreground mb-2">
                  <span className="font-medium capitalize">
                    {notification.recipients}
                  </span>
                  <span className="mx-1">•</span>
                  <span>{notification.type}</span>
                  <span className="mx-1">•</span>
                  <span className="capitalize">
                    {notification.createdByName ||
                      notification.createdBy?.name ||
                      "Unknown"}
                  </span>
                  <span className="mx-1">•</span>
                  <span className="capitalize">
                    {notification.createdByRole ||
                      notification.createdBy?.role ||
                      "Unknown Role"}
                  </span>
                </div>
                <p className="text-sm text-foreground/80 line-clamp-2 mb-2">
                  {notification.message}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">
                    {formatTimeAgo(notification.createdAt)}
                  </span>
                  <button
                    onClick={() => onView(notification._id)}
                    className="text-xs text-primary hover:text-primary/80 font-medium"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
            <DropdownActions
              item={notification}
              onView={onView}
              onEdit={onEdit}
              onSend={() => setOpenSend(true)}
              onDelete={() => setOpenDelete(true)}
              setOpenSend={setOpenSend}
              setOpenDelete={setOpenDelete}
              statusKey="status"
              sentStatus="sent"
              scheduledStatus="scheduled"
            />
          </div>
        </CardContent>
      </Card>

      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {notification.title}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this notification? This action
              cannot be undone.
              {notification.status === "sent" && (
                <span className="block mt-2 text-amber-600 font-medium">
                  Note: This notification has already been sent to recipients.
                </span>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={openSend} onOpenChange={setOpenSend}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Send {notification.title} now?</AlertDialogTitle>
            <AlertDialogDescription>
              This will send the notification immediately instead of waiting for
              the scheduled time. Are you sure you want to proceed?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleSend}>Send Now</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
