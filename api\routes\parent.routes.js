import express from "express";
import {
  createParent,
  getAllParents,
  getParentById,
  updateParent,
  deleteParent,
  updateParentStatus,
} from "../controllers/parent.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin"]),
  createParent
);
router.get("/", protect, authorize(["admin", "school-admin"]), getAllParents);
router.get(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  getParentById
);
router.put("/:id", protect, authorize(["admin", "school-admin"]), updateParent);
router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin"]),
  deleteParent
);
router.patch(
  "/:id/status",
  protect,
  authorize(["admin", "school-admin"]),
  updateParentStatus
);

export default router;
