import {
  UsersIcon,
  GraduationCapIcon,
  CalendarIcon,
  ClipboardListIcon,
  BookOpenIcon,
  BarChart3Icon,
  MessageSquareIcon,
  ShieldCheckIcon,
} from "lucide-react";
import {
  Item,
  ItemIcon,
  ItemTitle,
  ItemDescription,
} from "@/components/ui/item";
import { Section } from "@/components/ui/section";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";

export default function Items({
  title = "Comprehensive School Management Features",
  subtitle = "Everything you need to run your educational institution efficiently",
  badge = (
    <Announcement>
      <AnnouncementTag>FEATURES</AnnouncementTag>
      <AnnouncementTitle>All included in every plan</AnnouncementTitle>
    </Announcement>
  ),
  items = [
    {
      title: "Student Management",
      description:
        "Complete student profiles, enrollment tracking, and academic history",
      icon: <UsersIcon className="size-5 stroke-1" />,
    },
    {
      title: "Teacher Portal",
      description:
        "Tools for educators to manage classes, assignments, and grading",
      icon: <GraduationCapIcon className="size-5 stroke-1" />,
    },
    {
      title: "Attendance Tracking",
      description: "Automated attendance systems with real-time reporting",
      icon: <CalendarIcon className="size-5 stroke-1" />,
    },
    {
      title: "Exam Management",
      description:
        "Create, schedule, and grade exams with comprehensive analytics",
      icon: <ClipboardListIcon className="size-5 stroke-1" />,
    },
    {
      title: "Curriculum Planning",
      description:
        "Design and manage course content, syllabi, and learning objectives",
      icon: <BookOpenIcon className="size-5 stroke-1" />,
    },
    {
      title: "Performance Analytics",
      description:
        "Detailed insights into student and institutional performance",
      icon: <BarChart3Icon className="size-5 stroke-1" />,
    },
    {
      title: "Parent Communication",
      description: "Keep parents informed with automated updates and messaging",
      icon: <MessageSquareIcon className="size-5 stroke-1" />,
    },
    {
      title: "Data Security",
      description:
        "Enterprise-grade security to protect sensitive educational data",
      icon: <ShieldCheckIcon className="size-5 stroke-1" />,
    },
  ],
}) {
  return (
    <Section className="group relative overflow-hidden">
      <Container className="flex flex-col items-center gap-8 ">
        {badge !== false && badge}
        <h2 className="max-w-[640px] text-center text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
          {title}
          {subtitle && (
            <p className="mt-4 text-lg leading-relaxed text-muted-foreground sm:text-xl">
              {subtitle}
            </p>
          )}
        </h2>
        {items !== false && items.length > 0 && (
          <div className="grid auto-rows-fr grid-cols-2 gap-0 sm:grid-cols-3 sm:gap-4 lg:grid-cols-4">
            {items.map((item, index) => (
              <Item key={index}>
                <ItemTitle className="flex items-center gap-2">
                  <ItemIcon>{item.icon}</ItemIcon>
                  {item.title}
                </ItemTitle>
                <ItemDescription>{item.description}</ItemDescription>
              </Item>
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}
