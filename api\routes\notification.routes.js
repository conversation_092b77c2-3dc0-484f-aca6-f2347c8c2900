import express from "express";
import {
  createNotification,
  getAllNotifications,
  getNotificationById,
  updateNotification,
  deleteNotification,
  sendNotification,
  markAsRead,
} from "../controllers/notification.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post(
  "/create",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  createNotification
);
router.get("/", protect, getAllNotifications);
router.get("/:id", protect, getNotificationById);
router.put(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  updateNotification
);
router.delete(
  "/:id",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  deleteNotification
);
router.post(
  "/:id/send",
  protect,
  authorize(["admin", "school-admin", "teacher"]),
  sendNotification
);
router.post("/:id/read", protect, markAsRead);

export default router;
