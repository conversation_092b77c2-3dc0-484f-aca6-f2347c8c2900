import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { Building, User, MapPin, FileText } from "lucide-react";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";

const departmentTypes = [
  { value: "academic", label: "Academic" },
  { value: "administrative", label: "Administrative" },
  { value: "support", label: "Support" },
  { value: "research", label: "Research" },
];

const statusOptions = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
];

export function DepartmentForm({ editingId, initialData }) {
  const navigate = useNavigate();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      type: initialData?.type || "",
      establishedYear: initialData?.establishedYear || "",
      status: initialData?.status || "active",
      description: initialData?.description || "",
      
      // Department Head Information
      head: initialData?.head || "",
      headTitle: initialData?.headTitle || "",
      headEmail: initialData?.headEmail || "",
      headPhone: initialData?.headPhone || "",
      
      // Contact & Location
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      building: initialData?.building || "",
      floor: initialData?.floor || "",
    },
  });

  const handleSubmit = async (data) => {
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      if (editingId) {
        // Update existing department
        toast.success("Department updated successfully!", {
          description: `${data.name} has been updated.`,
        });
        navigate("/dashboard/academics/departments");
      } else {
        // Create new department
        toast.success("Department created successfully!", {
          description: `${data.name} has been added to the department list.`,
        });
        navigate("/dashboard/academics/departments");
      }
    } catch (error) {
      console.error("Department form submission error:", error);
      toast.error("Failed to save department", {
        description: "Please try again later.",
      });
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        {/* Basic Information */}
        <FormCard title="Basic Information" icon={Building}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              form={form}
              name="name"
              label="Department Name"
              placeholder="e.g., Computer Science"
              validation={{ required: "Department name is required" }}
            />
            <SelectInput
              form={form}
              name="type"
              label="Department Type"
              placeholder="Select type"
              options={departmentTypes}
              validation={{ required: "Department type is required" }}
            />
            <DateInput
              form={form}
              name="establishedYear"
              label="Established Year"
              placeholder="YYYY-MM-DD"
              validation={{ required: "Established year is required" }}
            />
            <SelectInput
              form={form}
              name="status"
              label="Status"
              placeholder="Select status"
              options={statusOptions}
              validation={{ required: "Status is required" }}
            />
          </div>
          <div className="mt-4">
            <TextareaInput
              form={form}
              name="description"
              label="Description"
              placeholder="Brief description of the department..."
              validation={{ required: "Description is required" }}
              inputProps={{ rows: 3 }}
            />
          </div>
        </FormCard>

        {/* Department Head Information */}
        <FormCard title="Department Head" icon={User}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              form={form}
              name="head"
              label="Head Name"
              placeholder="Dr. John Smith"
              validation={{ required: "Department head name is required" }}
            />
            <TextInput
              form={form}
              name="headTitle"
              label="Title"
              placeholder="Professor & Department Head"
              validation={{ required: "Head title is required" }}
            />
            <TextInput
              form={form}
              name="headEmail"
              label="Email"
              type="email"
              placeholder="<EMAIL>"
              validation={{
                required: "Email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              }}
            />
            <PhoneInput
              form={form}
              name="headPhone"
              label="Phone Number"
              validation={{ required: "Phone number is required" }}
            />
          </div>
        </FormCard>

        {/* Contact & Location */}
        <FormCard title="Contact & Location" icon={MapPin}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              form={form}
              name="email"
              label="Department Email"
              type="email"
              placeholder="<EMAIL>"
              validation={{
                required: "Department email is required",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "Invalid email address",
                },
              }}
            />
            <PhoneInput
              form={form}
              name="phone"
              label="Department Phone"
              validation={{ required: "Department phone is required" }}
            />
            <TextInput
              form={form}
              name="building"
              label="Building"
              placeholder="Science Building"
              validation={{ required: "Building is required" }}
            />
            <TextInput
              form={form}
              name="floor"
              label="Floor/Room Details"
              placeholder="3rd Floor, Rooms 301-320"
              validation={{ required: "Floor/room details are required" }}
            />
          </div>
        </FormCard>

        {/* Form Footer */}
        <FormFooter
          href="/dashboard/academics/departments"
          editingId={editingId}
          title="Department"
        />
      </form>
    </Form>
  );
}
