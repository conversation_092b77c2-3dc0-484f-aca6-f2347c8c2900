import { useEffect } from "react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { FileInput } from "@/components/form-inputs/file-input";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import {
  User,
  Shield,
  FileText,
  Settings,
  Key,
  MapPin,
  UserCheck,
  Briefcase,
} from "lucide-react";
import {
  adminRoles,
  departments,
  qualifications,
  employmentTypes,
  genderOptions,
  bloodGroups,
  maritalStatuses,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useNavigate } from "react-router-dom";
import { useSchool } from "@/context/school-context";
import { useSchoolAdmin } from "@/context/school-admin-context";

export function SchoolAdminForm({ editingId, initialData }) {
  const form = useForm({
    defaultValues: {
      // Personal Information
      firstName: initialData?.firstName || "",
      lastName: initialData?.lastName || "",
      phone: initialData?.phone || "",
      alternatePhone: initialData?.alternatePhone || "",
      gender: initialData?.gender || "",
      dateOfBirth: initialData?.dateOfBirth || "",
      bloodGroup: initialData?.bloodGroup || "",
      maritalStatus: initialData?.maritalStatus || "",
      nationality: initialData?.nationality || "",
      religion: initialData?.religion || "",
      aadharNumber: initialData?.aadharNumber || "",
      panNumber: initialData?.panNumber || "",

      // Address Information
      currentAddress: initialData?.currentAddress || "",
      permanentAddress: initialData?.permanentAddress || "",
      city: initialData?.city || "",
      state: initialData?.state || "",
      country: initialData?.country || "",
      postalCode: initialData?.postalCode || "",

      // Administrative Details
      adminId: initialData?.adminId || "",
      role: initialData?.role || "",
      department: initialData?.department || "",
      joiningDate: initialData?.joiningDate || "",
      employmentType: initialData?.employmentType || "",

      // Professional Information
      qualification: initialData?.qualification || "",
      experience: initialData?.experience || "",
      designation: initialData?.designation || "",
      previousEmployer: initialData?.previousEmployer || "",
      specialization: initialData?.specialization || "",

      // Emergency Contact
      emergencyContactName: initialData?.emergencyContactName || "",
      emergencyContactRelationship:
        initialData?.emergencyContactRelationship || "",
      emergencyContactPhone: initialData?.emergencyContactPhone || "",
      emergencyContactEmail: initialData?.emergencyContactEmail || "",
      emergencyContactAddress: initialData?.emergencyContactAddress || "",

      // Documents & Media
      profilePhoto: initialData?.profilePhoto || "",
      identityProof: initialData?.identityProof || "",
      resume: initialData?.resume || "",
      educationalCertificates: initialData?.educationalCertificates || "",

      // System Access & Permissions
      schoolName: initialData?.schoolName || "",
      username: initialData?.username || "",
      email: initialData?.email || "",
      password: initialData?.password || "",
      isActive: initialData?.isActive || true,

      // Permissions
      canManageStudents: initialData?.canManageStudents || false,
      canManageTeachers: initialData?.canManageTeachers || false,
      canManageStaff: initialData?.canManageStaff || false,
      canManageClasses: initialData?.canManageClasses || false,
      canManageSubjects: initialData?.canManageSubjects || false,
      canManageExams: initialData?.canManageExams || false,
      canManageAttendance: initialData?.canManageAttendance || false,
      canManageFees: initialData?.canManageFees || false,
      canManageReports: initialData?.canManageReports || false,
      canManageSettings: initialData?.canManageSettings || false,
      canManageAdmissions: initialData?.canManageAdmissions || false,
      canManageEvents: initialData?.canManageEvents || false,

      // Additional Information
      bio: initialData?.bio || "",
      skills: initialData?.skills || "",
      languages: initialData?.languages || "",
      hobbies: initialData?.hobbies || "",
      achievements: initialData?.achievements || "",
      notes: initialData?.notes || "",
    },
  });

  const navigate = useNavigate();

  const { addSchoolAdmin, editSchoolAdmin } = useSchoolAdmin();

  const handleSubmit = async (data) => {
    if (data.schoolName) {
      data.schoolId = data.schoolName.id;
      data.schoolName = data.schoolName.name;
    }
    try {
      if (editingId) {
        await editSchoolAdmin(editingId, data);
        navigate("/dashboard/school-admins");
        toast.success("School admin updated successfully!");
      } else {
        await addSchoolAdmin(data);
        // form.reset();
        toast.success("School admin created successfully!");
      }
    } catch (error) {
      console.error("School admin form submission error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to save school admin. Please try again."
      );
    }
    console.log(data);
  };

  const { fetchAllSchools, schoolOptions } = useSchool();

  useEffect(() => {
    fetchAllSchools();
  }, []);

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-12 gap-6">
            <div className="lg:col-span-12 col-span-full space-y-6">
              <FormCard title="Personal Information" icon={User}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="firstName"
                    label="First Name"
                    placeholder="Enter first name"
                    validation={{ required: "First name is required" }}
                  />
                  <TextInput
                    form={form}
                    name="lastName"
                    label="Last Name"
                    placeholder="Enter last name"
                    validation={{ required: "Last name is required" }}
                  />

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="gender"
                        label="Gender"
                        placeholder="Select gender"
                        options={genderOptions}
                        validation={{ required: "Gender is required" }}
                      />
                      <DateInput
                        form={form}
                        name="dateOfBirth"
                        label="Date of Birth"
                        placeholder="YYYY-MM-DD"
                        validation={{ required: "Date of birth is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="bloodGroup"
                        label="Blood Group"
                        placeholder="Select blood group"
                        options={bloodGroups}
                      />
                    </div>
                  </div>
                  <PhoneInput
                    form={form}
                    name="phone"
                    label="Phone Number"
                    validation={{ required: "Phone number is required" }}
                  />
                  <PhoneInput
                    form={form}
                    name="alternatePhone"
                    label="Alternate Phone"
                  />

                  <div className="md:col-span-2">
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                      <SelectInput
                        form={form}
                        name="maritalStatus"
                        label="Marital Status"
                        placeholder="Select marital status"
                        options={maritalStatuses}
                      />
                      <TextInput
                        form={form}
                        name="nationality"
                        label="Nationality"
                        placeholder="Enter nationality"
                        validation={{ required: "Nationality is required" }}
                      />
                      <TextInput
                        form={form}
                        name="religion"
                        label="Religion"
                        placeholder="Enter religion"
                      />
                    </div>
                  </div>
                  <TextInput
                    form={form}
                    name="aadharNumber"
                    label="Aadhar Number"
                    placeholder="Enter 12-digit Aadhar number"
                    validation={{
                      pattern: {
                        value: /^\d{12}$/,
                        message: "Aadhar number must be 12 digits",
                      },
                    }}
                  />
                  <TextInput
                    form={form}
                    name="panNumber"
                    label="PAN Number"
                    placeholder="Enter PAN number"
                    validation={{
                      pattern: {
                        value: /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/,
                        message: "Invalid PAN number format",
                      },
                    }}
                  />
                </div>
              </FormCard>

              <FormCard title="Address Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="city"
                    label="City"
                    placeholder="Enter city"
                    validation={{ required: "City is required" }}
                  />
                  <TextInput
                    form={form}
                    name="state"
                    label="State"
                    placeholder="Enter state"
                    validation={{ required: "State is required" }}
                  />
                  <TextInput
                    form={form}
                    name="country"
                    label="Country"
                    placeholder="Enter country"
                    validation={{ required: "Country is required" }}
                  />
                  <TextInput
                    form={form}
                    name="postalCode"
                    label="Postal Code"
                    placeholder="Enter postal code"
                    validation={{ required: "Postal code is required" }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="currentAddress"
                      label="Current Address"
                      placeholder="Enter current address"
                      validation={{ required: "Current address is required" }}
                    />
                  </div>
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="permanentAddress"
                      label="Permanent Address"
                      placeholder="Enter permanent address"
                      validation={{ required: "Permanent address is required" }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Administrative Details" icon={UserCheck}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="adminId"
                    label="Admin ID"
                    placeholder="Enter admin ID"
                    validation={{ required: "Admin ID is required" }}
                  />
                  <SelectInput
                    form={form}
                    name="role"
                    label="Role"
                    placeholder="Select role"
                    options={adminRoles}
                    validation={{ required: "Role is required" }}
                  />

                  <SelectInput
                    form={form}
                    name="department"
                    label="Department"
                    placeholder="Select department"
                    options={departments}
                    validation={{ required: "Department is required" }}
                  />
                  <div className="lg:col-span-3">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <DateInput
                        form={form}
                        name="joiningDate"
                        label="Joining Date"
                        placeholder="YYYY-MM-DD"
                        validation={{ required: "Joining date is required" }}
                      />
                      <SelectInput
                        form={form}
                        name="employmentType"
                        label="Employment Type"
                        placeholder="Select employment type"
                        options={employmentTypes}
                        validation={{ required: "Employment type is required" }}
                      />
                    </div>
                  </div>
                </div>
              </FormCard>

              <FormCard title="Professional Information" icon={Briefcase}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <SelectInput
                    form={form}
                    label="Qualification"
                    name="qualification"
                    placeholder="Select highest qualification"
                    options={qualifications}
                    validation={{ required: "Qualification is required" }}
                  />
                  <TextInput
                    form={form}
                    name="experience"
                    label="Experience (Years)"
                    type="number"
                    placeholder="Enter years of experience"
                    validation={{ required: "Experience is required" }}
                  />
                  <TextInput
                    form={form}
                    name="previousEmployer"
                    label="Previous Employer"
                    placeholder="Enter previous employer name"
                  />

                  <TextInput
                    form={form}
                    name="designation"
                    label="Designation"
                    placeholder="Enter designation/job title"
                    validation={{ required: "Designation is required" }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="specialization"
                      label="Specialization/Skills"
                      placeholder="Enter specialization or skills"
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Emergency Contact" icon={UserCheck}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="emergencyContactName"
                    label="Emergency Contact Name"
                    placeholder="Enter emergency contact name"
                    validation={{
                      required: "Emergency contact name is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="emergencyContactRelationship"
                    label="Relationship"
                    placeholder="Enter relationship"
                    validation={{ required: "Relationship is required" }}
                  />
                  <PhoneInput
                    form={form}
                    name="emergencyContactPhone"
                    label="Emergency Contact Phone"
                    validation={{
                      required: "Emergency contact phone is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="emergencyContactEmail"
                    label="Emergency Contact Email"
                    type="email"
                    placeholder="<EMAIL>"
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="emergencyContactAddress"
                      label="Emergency Contact Address"
                      placeholder="Enter emergency contact address"
                      validation={{
                        required: "Emergency contact address is required",
                      }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Documents & Media" icon={FileText}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FileInput
                    form={form}
                    name="profilePhoto"
                    label="Profile Photo"
                    description="Upload profile photo (PNG, JPG, JPEG)"
                    accept="image/*"
                  />

                  <FileInput
                    form={form}
                    name="identityProof"
                    label="Identity Proof"
                    description="Upload identity proof (PDF, JPG, PNG)"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                  <FileInput
                    form={form}
                    name="resume"
                    label="Resume/CV"
                    description="Upload resume or CV (PDF)"
                    accept=".pdf"
                  />
                  <FileInput
                    form={form}
                    name="educationalCertificates"
                    label="Educational Certificates"
                    description="Upload educational certificates (PDF)"
                    accept=".pdf"
                  />
                </div>
              </FormCard>

              <FormCard title="System Access & Permissions" icon={Key}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <ComboboxInput
                    form={form}
                    name="schoolName"
                    label="School Name"
                    options={schoolOptions}
                    placeholder="Select school name"
                    href="/dashboard/schools/create"
                    toolTipText="This will redirect you to create/edit a new school."
                    validation={{ required: "School name is required" }}
                  />
                  <TextInput
                    form={form}
                    name="username"
                    label="Username"
                    placeholder="Enter username for login"
                    validation={{ required: "Username is required" }}
                  />
                  <TextInput
                    form={form}
                    name="email"
                    label="Email Address"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                    toolTipText="This should be unique and used for logging in."
                  />
                  <PasswordInput
                    form={form}
                    name="password"
                    label="Password"
                    placeholder="Enter password for login"
                    validation={{ required: "Password is required" }}
                  />
                  <div className="md:col-span-2">
                    <CheckboxInput
                      form={form}
                      name="isActive"
                      label="Active Account"
                      description="Enable this to allow the user to log in."
                    />
                  </div>
                </div>

                <FormCard title="Module Permissions" icon={Shield}>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <CheckboxInput
                      form={form}
                      name="canManageStudents"
                      label="Manage Students"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageTeachers"
                      label="Manage Teachers"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageStaff"
                      label="Manage Staff"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageClasses"
                      label="Manage Classes"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageSubjects"
                      label="Manage Subjects"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageExams"
                      label="Manage Exams"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageAttendance"
                      label="Manage Attendance"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageFees"
                      label="Manage Fees"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageReports"
                      label="Manage Reports"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageSettings"
                      label="Manage Settings"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageAdmissions"
                      label="Manage Admissions"
                    />
                    <CheckboxInput
                      form={form}
                      name="canManageEvents"
                      label="Manage Events"
                    />
                  </div>
                </FormCard>
              </FormCard>

              <FormCard title="Additional Information" icon={Settings}>
                <div className="grid grid-cols-1 gap-4">
                  <TextareaInput
                    form={form}
                    name="bio"
                    label="Bio"
                    placeholder="Enter a brief professional bio"
                    inputProps={{ rows: 3 }}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <TextareaInput
                      form={form}
                      name="skills"
                      label="Skills"
                      placeholder="Enter skills (comma separated)"
                      inputProps={{ rows: 2 }}
                    />
                    <TextareaInput
                      form={form}
                      name="languages"
                      label="Languages Known"
                      placeholder="Enter languages (comma separated)"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <TextareaInput
                      form={form}
                      name="hobbies"
                      label="Hobbies"
                      placeholder="Enter hobbies (comma separated)"
                      inputProps={{ rows: 2 }}
                    />
                    <TextareaInput
                      form={form}
                      name="achievements"
                      label="Achievements"
                      placeholder="Enter professional achievements"
                      inputProps={{ rows: 2 }}
                    />
                  </div>
                  <TextareaInput
                    form={form}
                    name="notes"
                    label="Additional Notes"
                    placeholder="Enter any additional notes or comments"
                    inputProps={{ rows: 3 }}
                  />
                </div>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="/school-admins"
            parent=""
            title="School Admin"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
