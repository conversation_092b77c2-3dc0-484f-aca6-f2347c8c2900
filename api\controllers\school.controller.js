import School from "../models/school.model.js";

// Create a new school
export const createSchool = async (req, res) => {
  try {
    const schoolData = req.body;

    const newSchool = new School(schoolData);
    await newSchool.save();

    res.status(201).json({
      success: true,
      message: "School created successfully",
      data: newSchool,
    });
  } catch (error) {
    console.error("Create School Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

export const getAllSchools = async (req, res) => {
  try {
    const schools = await School.find().sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: schools.length,
      data: schools,
    });
  } catch (error) {
    console.error("Get All Schools Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const getSchoolById = async (req, res) => {
  try {
    const school = await School.findById(req.params.id);

    if (!school) {
      return res.status(404).json({
        success: false,
        message: "School not found",
      });
    }

    res.status(200).json({
      success: true,
      data: school,
    });
  } catch (error) {
    console.error("Get School By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const updateSchool = async (req, res) => {
  try {
    const schoolData = req.body;

    const school = await School.findByIdAndUpdate(req.params.id, schoolData, {
      new: true,
      runValidators: true,
    });

    if (!school) {
      return res.status(404).json({
        success: false,
        message: "School not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "School updated successfully",
      data: school,
    });
  } catch (error) {
    console.error("Update School Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const deleteSchool = async (req, res) => {
  try {
    const school = await School.findByIdAndDelete(req.params.id);

    if (!school) {
      return res.status(404).json({
        success: false,
        message: "School not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "School deleted successfully",
    });
  } catch (error) {
    console.error("Delete School Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const updateSchoolStatus = async (req, res) => {
  try {
    const { status } = req.body;

    if (
      !status ||
      !["active", "inactive", "pending", "suspended"].includes(status)
    ) {
      return res.status(400).json({
        success: false,
        message: "Please provide a valid status",
      });
    }

    const school = await School.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );

    if (!school) {
      return res.status(404).json({
        success: false,
        message: "School not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "School status updated successfully",
      data: school,
    });
  } catch (error) {
    console.error("Update School Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
