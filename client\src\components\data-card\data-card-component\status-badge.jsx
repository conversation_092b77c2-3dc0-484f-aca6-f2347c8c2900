import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

export function StatusBadge({
  row,
  className,
  showText = true,
  statusField = "status",
  variant = {
    default: "default",
    secondary: "secondary",
    warning: "warning",
    success: "success",
    outline: "outline",
    destructive: "destructive",
  },
}) {
  const status = row.original[statusField];
  const badgeVariant = variant[status] || "outline";

  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        className,
        "capitalize",
        !showText && "rounded-full w-2 h-2 p-0"
      )}
    >
      {showText ? status : null}
    </Badge>
  );
}
