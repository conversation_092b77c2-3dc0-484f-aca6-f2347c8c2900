import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar as CalendarIcon, CircleHelp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

export function DateInput({
  form,
  name,
  label,
  className,
  placeholder = "Select a date",
  onChange,
  id,
  mindate,
  maxdate,
  description,
  toolTipText,
  validation = {},
  ...props
}) {
  const [open, setOpen] = useState(false);

  const fieldId = id || `date-input-${name}`;

  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel htmlFor={fieldId} className="flex items-center gap-2">
            {label}
            {toolTipText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <CircleHelp className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">{toolTipText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </FormLabel>

          <div className="relative w-full pt-2">
            <Popover open={open} onOpenChange={setOpen}>
              <PopoverTrigger asChild>
                <Button
                  id={fieldId}
                  variant="outline"
                  role="combobox"
                  aria-expanded={open}
                  className={cn(
                    "w-full justify-start text-left pl-10",
                    !field.value && "text-muted-foreground",
                    className
                  )}
                  {...props}
                >
                  {field.value ? format(field.value, "PPP") : placeholder}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <FormControl>
                  <Calendar
                    mode="single"
                    minDate={mindate || 1970}
                    maxDate={maxdate || new Date()}
                    selected={field.value}
                    onSelect={field.onChange}
                    initialFocus
                  />
                </FormControl>
              </PopoverContent>
            </Popover>
            <CalendarIcon className="absolute left-3 top-4.5 h-4 w-4 pointer-events-none text-muted-foreground" />
          </div>

          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
