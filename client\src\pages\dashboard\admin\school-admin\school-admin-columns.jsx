import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { ImageColumn } from "@/components/data-table/data-table-columns/image-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { useSchoolAdmin } from "@/context/school-admin-context";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

export const SchoolAdminColumns = () => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumn column={column} title="Admin Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <ImageColumn
            src={row.original.profilePhoto}
            alt={`${row.original.firstName} ${row.original.lastName}`}
            fallbackText={`${row.original.firstName?.charAt(0) || ""}${
              row.original.lastName?.charAt(0) || ""
            }`}
          />
          <div className="flex flex-col">
            <div className="font-medium">
              {row.original.firstName} {row.original.lastName}
            </div>
            <div className="text-sm text-muted-foreground">
              {row.original.adminId}
            </div>
          </div>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "role",
      header: ({ column }) => <SortableColumn column={column} title="Role" />,
      cell: ({ row }) => (
        <div className="text-sm uppercase text-center w-full">
          {row.original.role}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "department",
      header: ({ column }) => (
        <SortableColumn column={column} title="Department" />
      ),
      cell: ({ row }) => (
        <div className="text-sm text-center w-full">
          {row.original.department}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "school",
      header: ({ column }) => <SortableColumn column={column} title="School" />,
      cell: ({ row }) => {
        let schoolId = row.original.schoolId;
        if (schoolId) {
          schoolId = schoolId._id || schoolId.id || JSON.stringify(schoolId);
        }
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium">{row.original.schoolName}</div>
            <div className="text-xs text-muted-foreground">{schoolId}</div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "contact",
      header: ({ column }) => (
        <SortableColumn column={column} title="Contact" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm">{row.original.email}</div>
            <div className="text-xs text-muted-foreground">
              {row.original.phone}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "location",
      header: ({ column }) => (
        <SortableColumn column={column} title="Location" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm">
              {row.original.city}, {row.original.state}
            </div>
            <div className="text-xs text-muted-foreground">
              {row.original.country}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "employmentType",
      header: ({ column }) => (
        <SortableColumn column={column} title="Employment" />
      ),
      cell: ({ row }) => (
        <StatusColumn
          row={row}
          statusField="employmentType"
          variant={{
            "Full Time": "success",
            "Part Time": "secondary",
            Contract: "warning",
            Temporary: "default",
          }}
        />
      ),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        return (
          <StatusColumn
            row={row}
            statusField="status"
            variant={{
              active: "success",
              inactive: "destructive",
              suspended: "warning",
            }}
          />
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "joiningDate",
      header: "Joining Date",
      cell: ({ row }) => <DateColumn row={row} accessorKey="joiningDate" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      accessorKey: "createdAt",
      header: "Date Added",
      cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => <SchoolAdminActions row={row} />,
    },
  ];
};

const SchoolAdminActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeSchoolAdmin } = useSchoolAdmin();

  const handleView = (e) => {
    navigate(`/dashboard/school-admins/${row.original._id}`);
  };

  const handleEdit = (e) => {
    navigate(`/dashboard/school-admins/${row.original._id}/edit`);
  };

  const [open, setOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await removeSchoolAdmin(row.original._id);
      toast.success(
        `${row.original.firstName} ${row.original.lastName} deleted successfully.`
      );
      setOpen(false);
    } catch (error) {
      console.error("Failed to delete school admin:", error);
      toast.error("Failed to delete school admin. Please try again.");
    }
  };

  return (
    <>
      <ActionColumn
        row={row}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={() => setOpen(true)}
      />
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Delete {row.original.firstName} {row.original.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this school admin? This action
              cannot be undone and will also remove their user account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
