import express from "express";
import {
  submitContactForm,
  getAllContactSubmissions,
  getContactSubmissionById,
  updateContactSubmissionStatus,
} from "../controllers/contact.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post("/create", submitContactForm);
router.get("/", protect, authorize(["admin"]), getAllContactSubmissions);
router.get("/:id", protect, authorize(["admin"]), getContactSubmissionById);
router.put(
  "/:id/status",
  protect,
  authorize(["admin"]),
  updateContactSubmissionStatus
);

export default router;
