import React from "react";
import { cn } from "@/lib/utils";
import { Section } from "@/components/ui/section";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";
import { CheckCircle, Clock } from "lucide-react";

export default function Timeline({
  title = "Our Implementation Roadmap",
  subtitle = "A streamlined process to get your school up and running with School TRACK",
  badge = (
    <Announcement>
      <AnnouncementTag>TIMELINE</AnnouncementTag>
      <AnnouncementTitle>
        From onboarding to full implementation
      </AnnouncementTitle>
    </Announcement>
  ),
  items = [
    {
      title: "Initial Consultation",
      description:
        "We meet with your team to understand your specific needs and requirements.",
      duration: "Week 1",
      status: "completed",
    },
    {
      title: "System Configuration",
      description:
        "Our team configures the platform according to your school's structure and processes.",
      duration: "Week 2-3",
      status: "completed",
    },
    {
      title: "Data Migration",
      description:
        "We securely transfer your existing data into the School TRACK system.",
      duration: "Week 3-4",
      status: "active",
    },
    {
      title: "Staff Training",
      description:
        "Comprehensive training sessions for administrators, teachers, and support staff.",
      duration: "Week 5",
      status: "upcoming",
    },
    {
      title: "Pilot Launch",
      description:
        "A controlled rollout to a select group of users to ensure everything works smoothly.",
      duration: "Week 6",
      status: "upcoming",
    },
    {
      title: "Full Implementation",
      description:
        "Complete deployment across your entire institution with ongoing support.",
      duration: "Week 7-8",
      status: "upcoming",
    },
  ],
  className,
}) {
  return (
    <Section className={cn("group relative", className)}>
      <Container className="flex flex-col items-center gap-12">
        <div className="flex flex-col items-center gap-4 text-center">
          {badge !== false && badge}
          {title && (
            <h2 className="text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-md text-muted-foreground max-w-[600px] font-medium sm:text-xl">
              {subtitle}
            </p>
          )}
        </div>

        {items && items.length > 0 && (
          <div className="relative w-full max-w-3xl mx-auto">
            {/* Vertical line */}
            <div className="absolute left-[20px] sm:left-1/2 top-0 bottom-0 w-[2px] bg-border -translate-x-1/2 z-0"></div>

            <div className="flex flex-col gap-8">
              {items.map((item, index) => (
                <div
                  key={index}
                  className={cn(
                    "relative flex flex-col sm:flex-row gap-4 sm:gap-8",
                    index % 2 === 0 ? "sm:flex-row-reverse" : ""
                  )}
                >
                  {/* Timeline dot */}
                  <div className="absolute left-[20px] sm:left-1/2 top-0 -translate-x-1/2 z-10">
                    {item.status === "completed" ? (
                      <CheckCircle className="size-10 p-2 bg-primary text-primary-foreground rounded-full" />
                    ) : item.status === "active" ? (
                      <Clock className="size-10 p-2 bg-brand text-brand-foreground rounded-full animate-pulse" />
                    ) : (
                      <div className="size-10 p-2 bg-muted rounded-full" />
                    )}
                  </div>

                  {/* Content */}
                  <div
                    className={cn(
                      "flex flex-col gap-2 pl-16 sm:pl-0 sm:w-[calc(50%-24px)]",
                      item.status === "completed"
                        ? "opacity-90"
                        : item.status === "active"
                        ? "opacity-100"
                        : "opacity-70"
                    )}
                  >
                    <div className="flex flex-col">
                      <h3 className="text-lg font-semibold">{item.title}</h3>
                      <span className="text-sm text-muted-foreground">
                        {item.duration}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {item.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Container>
    </Section>
  );
}
