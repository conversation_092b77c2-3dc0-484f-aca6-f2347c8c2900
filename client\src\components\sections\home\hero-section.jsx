import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRightIcon } from "lucide-react";
import { Mockup, MockupFrame } from "@/components/ui/mockup";
import Glow from "@/components/ui/glow";
import Github from "@/components/logos/github";
import Screenshot from "@/components/ui/screenshot";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";

export default function Hero({
  title = "Transform Your School Management Experience",
  description = "School TRACK provides a comprehensive solution for educational institutions to manage students, teachers, classes, attendance, grades, and administrative tasks all in one place.",
  mockup = (
    <Screenshot
      srcLight="/light.png"
      srcDark="/dark.png"
      alt="School Management System dashboard screenshot"
      width={1248}
      height={765}
      className="w-full"
    />
  ),

  badge = (
    <Announcement>
      <AnnouncementTag>NEW</AnnouncementTag>
      <AnnouncementTitle>
        School TRACK v1.0 is now available!
        <a
          href="/whats-new"
          className="ml-2 flex items-center text-sm text-primary"
        >
          See What's New
          <ArrowRightIcon className="ml-1 size-3" />
        </a>
      </AnnouncementTitle>
    </Announcement>
  ),

  buttons = [
    {
      href: "/dashboard",
      text: "Dashboard",
      variant: "default",
    },
    {
      href: "/dashboard",
      text: "Admin Login",
      variant: "ghost",
      icon: <Github className="mr-2 size-4" />,
    },
  ],

  className,
}) {
  return (
    <div
      className={cn(
        "fade-bottom overflow-hidden pb-0 sm:pb-0 md:pb-0",
        className
      )}
    >
      <Container className="gap-12 pt-12 sm:gap-24">
        <div className="flex flex-col items-center gap-6 text-center sm:gap-12">
          {badge !== false && badge}
          <h1 className="animate-appear from-foreground to-foreground dark:to-muted-foreground relative z-10 inline-block bg-linear-to-r bg-clip-text text-4xl leading-tight font-semibold text-balance text-transparent drop-shadow-2xl sm:text-4xl sm:leading-tight md:text-6xl md:leading-tight">
            {title}
          </h1>
          <p className="text-md animate-appear text-muted-foreground relative z-10 max-w-[740px] font-medium text-balance opacity-0 delay-100 sm:text-xl">
            {description}
          </p>
          {buttons && buttons.length > 0 && (
            <div className="animate-appear relative z-10 flex justify-center gap-4 opacity-0 delay-300">
              {buttons.map((button, index) => (
                <Button
                  key={index}
                  variant={button.variant || "default"}
                  size="lg"
                  asChild
                >
                  <a href={button.href}>
                    {button.icon}
                    {button.text}
                    {button.iconRight}
                  </a>
                </Button>
              ))}
            </div>
          )}
          {mockup !== false && (
            <div className="relative w-full pt-12">
              <MockupFrame
                className="animate-appear opacity-0 delay-700"
                size="small"
              >
                <Mockup
                  type="responsive"
                  className="bg-background/90 w-full rounded-xl border-0"
                >
                  {mockup}
                </Mockup>
              </MockupFrame>
              <Glow
                variant="top"
                className="animate-appear-zoom opacity-0 delay-1000"
              />
            </div>
          )}
        </div>
      </Container>
    </div>
  );
}
