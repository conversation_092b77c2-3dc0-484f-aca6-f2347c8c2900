import React from "react";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { CircleHelp } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import AddNewButton from "./add-new-button";

export function SelectInput({
  form,
  name,
  label,
  icon: Icon,
  placeholder,
  description,
  toolTipText,
  href,
  options = [],
  validation = {},
  selectProps = {},
}) {
  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="flex items-center gap-2">
            {label}
            {toolTipText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <CircleHelp className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">{toolTipText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </FormLabel>
          <div className="relative">
            {Icon && (
              <div className="absolute top-4 left-3 pointer-events-none">
                <Icon className="h-5 w-5" />
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              {...selectProps}
            >
              <FormControl>
                <SelectTrigger className={Icon ? "pl-10 w-full" : "w-full"}>
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              </FormControl>

              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
              {href && toolTipText && (
                <AddNewButton toolTipText={toolTipText} href={href} />
              )}
            </Select>
          </div>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
