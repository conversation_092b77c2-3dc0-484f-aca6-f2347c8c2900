import React from "react";
import { <PERSON><PERSON>elp } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";

export function TextareaInput({
  form,
  name,
  label,
  icon: Icon,
  placeholder,
  description,
  toolTipText,
  validation = {},
  inputProps = {},
}) {
  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="flex items-center gap-2">
            {label}
            {toolTipText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <CircleHelp className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">{toolTipText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </FormLabel>
          <div className="relative">
            {Icon && (
              <div className="absolute top-5 left-3 pointer-events-none">
                <Icon className="h-5 w-5" />
              </div>
            )}
          </div>
          <FormControl>
            <Textarea
              placeholder={placeholder}
              {...field}
              {...inputProps}
              className={Icon ? "pl-10" : ""}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
