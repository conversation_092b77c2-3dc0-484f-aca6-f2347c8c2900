import React from "react";
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { CircleHelp } from "lucide-react";

export function RadioInput({
  form,
  name,
  label,
  className,
  options = [],
  onChange,
  value,
  orientation = "vertical",
  description,
  toolTipText,
  validation = {},
  ...props
}) {
  const handleValueChange = (newValue, fieldOnChange) => {
    fieldOnChange(newValue);

    if (onChange) {
      onChange(newValue);
    }
  };

  const getOptionId = (optionValue) => `${name}-${optionValue}`;

  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <fieldset className="space-y-3">
            <legend className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2">
              {label}
              {toolTipText && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-4 w-4 p-0"
                      >
                        <CircleHelp className="h-4 w-4 text-muted-foreground" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="text-sm">{toolTipText}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </legend>

            <FormControl>
              <RadioGroup
                value={field.value}
                onValueChange={(value) =>
                  handleValueChange(value, field.onChange)
                }
                className={cn(
                  orientation === "horizontal" ? "flex space-x-4" : "space-y-2",
                  className
                )}
                {...props}
              >
                {options.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center space-x-2"
                  >
                    <RadioGroupItem
                      value={option.value}
                      id={getOptionId(option.value)}
                    />
                    <Label htmlFor={getOptionId(option.value)}>
                      {option.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            </FormControl>

            {description && <FormDescription>{description}</FormDescription>}
            <FormMessage />
          </fieldset>
        </FormItem>
      )}
    />
  );
}
