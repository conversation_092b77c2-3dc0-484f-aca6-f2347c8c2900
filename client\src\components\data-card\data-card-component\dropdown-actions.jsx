import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreVertical, Bell, Edit, Send, Trash2 } from "lucide-react";

export const DropdownActions = ({
  item,
  onView,
  onEdit,
  onSend,
  onDelete,
  setOpenSend,
  setOpenDelete,
  statusKey = "status",
  sentStatus = "sent",
  scheduledStatus = "scheduled",
}) => {
  return (
    <div className="flex-shrink-0 ml-4">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-muted-foreground hover:text-foreground"
          >
            <MoreVertical className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => onView(item._id)}>
            <Bell className="mr-2 h-4 w-4" />
            View
          </DropdownMenuItem>
          {item[statusKey] !== sentStatus && (
            <DropdownMenuItem onClick={() => onEdit(item._id)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
          )}
          {item[statusKey] === scheduledStatus && (
            <DropdownMenuItem
              onClick={() => (onSend ? onSend(item._id) : setOpenSend?.(true))}
            >
              <Send className="mr-2 h-4 w-4" />
              Send Now
            </DropdownMenuItem>
          )}
          <DropdownMenuItem
            onClick={() =>
              onDelete ? onDelete(item._id) : setOpenDelete?.(true)
            }
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
