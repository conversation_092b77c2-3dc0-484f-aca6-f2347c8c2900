import React from "react";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, Edit, Trash2, Copy } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";

export const ActionColumn = ({
  row,
  actions = [],
  isLoading = false,
  onView,
  onEdit,
  onDelete,
  viewLabel = "View",
  editLabel = "Edit",
  deleteLabel = "Delete",
  showView = true,
  showEdit = true,
  showDelete = true,
}) => {
  if (isLoading) {
    return <Skeleton className="h-9 w-9" />;
  }

  const defaultActions = [];

  if (showView) {
    defaultActions.push({
      label: viewLabel,
      icon: Eye,
      onClick: (e) => {
        onView && onView(e, row);
      },
    });
  }

  if (showEdit) {
    defaultActions.push({
      label: editLabel,
      icon: Edit,
      onClick: (e) => {
        onEdit && onEdit(e, row);
      },
    });
  }

  if (showDelete) {
    defaultActions.push({
      label: deleteLabel,
      icon: Trash2,
      onClick: (e) => {
        onDelete && onDelete(e, row);
      },
      variant: "destructive",
    });
  }

  const actionsToRender = actions.length > 0 ? actions : defaultActions;

  return (
    <div className="flex justify-end">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {actionsToRender.map((action, index) => (
            <DropdownMenuItem
              key={index}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                action.onClick(e);
              }}
              variant={action.variant}
              disabled={action.disabled}
            >
              {action.icon && <action.icon className="h-4 w-4" />}
              {action.label}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
