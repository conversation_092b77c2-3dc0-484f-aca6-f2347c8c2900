import React, { useState } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Check, ChevronsUpDown, CircleHelp } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import AddNewButton from "@/components/form-inputs/add-new-button";

export function ComboboxInput({
  form,
  name,
  label,
  className,
  href,
  toolTipText,
  options = [],
  placeholder = "Select an option...",
  searchPlaceholder = "Search options...",
  emptyMessage = "No options found.",
  description,
  validation = {},
  ...props
}) {
  const [open, setOpen] = useState(false);

  const fieldId = `combobox-input-${name}`;

  const handleSelect = (selectedValue, fieldOnChange) => {
    const newValue = selectedValue === fieldOnChange ? "" : selectedValue;
    fieldOnChange(newValue);
    setOpen(false);
  };

  const getComparisonValue = (value) => {
    if (!value) return value;
    if (value.id) return value.id;
    return value;
  };

  const getCurrentOption = (value) => {
    return options.find(
      (option) => getComparisonValue(option.value) === getComparisonValue(value)
    );
  };

  const getDisplayValue = (value) => {
    const option = getCurrentOption(value);
    return option ? option.label : placeholder;
  };

  const isSelected = (optionValue, fieldValue) => {
    return getComparisonValue(optionValue) === getComparisonValue(fieldValue);
  };

  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel htmlFor={fieldId} className="flex items-center gap-2 mb-2">
            {label}
          </FormLabel>
          <div className="flex gap-2 w-full">
            <div className="relative flex-1">
              <Popover open={open} onOpenChange={setOpen}>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      id={fieldId}
                      variant="outline"
                      role="combobox"
                      aria-expanded={open}
                      className={cn(
                        "w-full justify-between",
                        !field.value && "text-muted-foreground",
                        className
                      )}
                      {...props}
                    >
                      <span className="truncate">
                        {getDisplayValue(field.value)}
                      </span>
                      <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent
                  className="p-0"
                  align="start"
                  sideOffset={5}
                  style={{ width: "var(--radix-popover-trigger-width)" }}
                >
                  <Command className="w-full">
                    <CommandInput placeholder={searchPlaceholder} />
                    <CommandList>
                      <CommandEmpty>{emptyMessage}</CommandEmpty>
                      <CommandGroup>
                        {options.map((option) => (
                          <CommandItem
                            key={getComparisonValue(option.value)}
                            value={option.label}
                            onSelect={() =>
                              handleSelect(option.value, field.onChange)
                            }
                            className="flex items-center justify-between"
                          >
                            <div className="flex items-center space-x-2">
                              {option.icon && (
                                <span className="flex-shrink-0">
                                  {option.icon}
                                </span>
                              )}
                              <div>
                                <div className="font-medium">
                                  {option.label}
                                </div>
                                {option.description && (
                                  <div className="text-sm text-muted-foreground">
                                    {option.description}
                                  </div>
                                )}
                              </div>
                            </div>
                            <Check
                              className={cn(
                                "ml-auto h-4 w-4",
                                isSelected(option.value, field.value)
                                  ? "opacity-100"
                                  : "opacity-0"
                              )}
                            />
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
            {href && toolTipText && (
              <AddNewButton toolTipText={toolTipText} href={href} />
            )}
          </div>

          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
