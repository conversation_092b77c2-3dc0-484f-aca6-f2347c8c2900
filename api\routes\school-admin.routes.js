import express from "express";
import {
  createSchoolAdmin,
  getAllSchoolAdmins,
  getSchoolAdminById,
  updateSchoolAdmin,
  deleteSchoolAdmin,
} from "../controllers/school-admin.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post("/create", protect, authorize(["admin"]), createSchoolAdmin);
router.get("/", protect, authorize(["admin"]), getAllSchoolAdmins);
router.get("/:id", protect, authorize(["admin"]), getSchoolAdminById);
router.put("/:id", protect, authorize(["admin"]), updateSchoolAdmin);
router.delete("/:id", protect, authorize(["admin"]), deleteSchoolAdmin);

export default router;
