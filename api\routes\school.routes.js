import express from "express";
import {
  createSchool,
  getAllSchools,
  getSchoolById,
  updateSchool,
  deleteSchool,
  updateSchoolStatus,
} from "../controllers/school.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post("/create", protect, authorize(["admin"]), createSchool);
router.get("/", protect, authorize(["admin"]), getAllSchools);
router.get("/:id", protect, authorize(["admin"]), getSchoolById);
router.put("/:id", protect, authorize(["admin"]), updateSchool);
router.delete("/:id", protect, authorize(["admin"]), deleteSchool);
router.patch("/:id/status", protect, authorize(["admin"]), updateSchoolStatus);

export default router;
