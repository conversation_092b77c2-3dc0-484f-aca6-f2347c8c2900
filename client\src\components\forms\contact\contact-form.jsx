import React from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import {
  User,
  Send,
  Building,
  GraduationCap,
  Users,
  BookOpen,
  School,
  Mail,
  Globe,
  HelpCircle,
} from "lucide-react";
import { ComboboxInput } from "@/components/form-inputs/combobox-input";
import { SubmitButton } from "@/components/form-inputs/submit-button";
import { useContact } from "@/context/contact-context";

const ContactForm = () => {
  const { submitForm, isLoading, error } = useContact();

  const form = useForm({
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      schoolName: "",
      schoolEmail: "",
      schoolWebsite: "",
      howDidYouHear: "",
      role: "",
      schoolSize: "",
      inquiryType: "",
      message: "",
      newsletter: false,
    },
  });

  const onSubmit = async (data) => {
    try {
      await submitForm(data);
      toast.success("Form Submitted Successfully", {
        description:
          "Thank you for your interest in School TRACK! Our education solutions team will contact you within 24 hours.",
      });
      form.reset();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Form Submission Failed", {
        description:
          error.message ||
          "We couldn't process your request. Please try again later or contact our support team.",
      });
    }
  };

  const roleOptions = [
    {
      value: "administrator",
      label: "School Administrator",
      description: "Principal, Vice Principal, Director",
      icon: <User className="h-4 w-4" />,
    },
    {
      value: "teacher",
      label: "Teacher",
      description: "Faculty member or instructor",
      icon: <GraduationCap className="h-4 w-4" />,
    },
    {
      value: "it",
      label: "IT Administrator",
      description: "Technology coordinator or IT staff",
      icon: <Building className="h-4 w-4" />,
    },
    {
      value: "parent",
      label: "Parent/Guardian",
      description: "Parent or guardian of student(s)",
      icon: <Users className="h-4 w-4" />,
    },
    {
      value: "other",
      label: "Other Role",
      description: "Other educational stakeholder",
      icon: <BookOpen className="h-4 w-4" />,
    },
  ];

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <TextInput
            form={form}
            name="name"
            label="Full Name"
            placeholder="Enter your full name"
            validation={{
              required: "Name is required",
            }}
          />

          <TextInput
            form={form}
            name="email"
            label="Email Address"
            type="email"
            placeholder="Enter your email address"
            validation={{
              required: "Email is required",
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Invalid email address",
              },
            }}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <PhoneInput
            form={form}
            name="phone"
            label="Phone Number"
            defaultCountry="IN"
            validation={{
              required: "Phone number is required",
            }}
          />

          <TextInput
            form={form}
            name="schoolName"
            label="School/Institution Name"
            placeholder="Enter your school or institution name"
            validation={{
              required: "School name is required",
            }}
            leftIcon={<School className="h-4 w-4" />}
          />

          <TextInput
            form={form}
            name="schoolEmail"
            label="School Email Address"
            type="email"
            placeholder="Enter school email address (if applicable)"
            leftIcon={<Mail className="h-4 w-4" />}
            validation={{
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Invalid email address",
              },
            }}
          />

          <TextInput
            form={form}
            name="schoolWebsite"
            label="School Website"
            placeholder="Enter school website URL (if applicable)"
            leftIcon={<Globe className="h-4 w-4" />}
            validation={{
              pattern: {
                value:
                  /^(https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
                message: "Invalid website URL",
              },
            }}
          />
        </div>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <ComboboxInput
            form={form}
            name="role"
            label="Your Role"
            options={roleOptions}
            placeholder="Select your role..."
            searchPlaceholder="Search roles..."
            emptyMessage="No roles found."
            allowClear={true}
            toolTipText="Select the role that best describes your position"
            validation={{
              required: "Please select your role",
            }}
          />

          <SelectInput
            form={form}
            name="schoolSize"
            label="School Size"
            placeholder="Select school size"
            options={[
              { label: "Small (< 500 students)", value: "small" },
              { label: "Medium (500-1000 students)", value: "medium" },
              { label: "Large (1000-2000 students)", value: "large" },
              { label: "Very Large (2000+ students)", value: "xlarge" },
              { label: "District/Multiple Schools", value: "district" },
            ]}
            validation={{
              required: "Please select your school size",
            }}
          />

          <SelectInput
            form={form}
            name="inquiryType"
            label="Inquiry Type"
            placeholder="Select inquiry type"
            options={[
              { label: "Product Information", value: "info" },
              { label: "Pricing & Packages", value: "pricing" },
              { label: "Implementation Support", value: "implementation" },
              { label: "Technical Support", value: "support" },
              { label: "Demo Request", value: "demo" },
              { label: "Partnership Opportunity", value: "partnership" },
              { label: "Other", value: "other" },
            ]}
            validation={{
              required: "Please select an inquiry type",
            }}
          />

          <SelectInput
            form={form}
            name="howDidYouHear"
            label="How did you hear about us?"
            placeholder="Select an option"
            leftIcon={<HelpCircle className="h-4 w-4" />}
            options={[
              { label: "Search Engine (Google, Bing, etc.)", value: "search" },
              { label: "Social Media", value: "social" },
              { label: "Email Newsletter", value: "email" },
              { label: "Conference/Event", value: "event" },
              { label: "Referral from another school", value: "referral" },
              { label: "Education Publication", value: "publication" },
              { label: "Advertisement", value: "ad" },
              { label: "Other", value: "other" },
            ]}
            validation={{
              required: "Please select how you heard about us",
            }}
          />
        </div>

        <TextareaInput
          form={form}
          name="message"
          label="Message"
          placeholder="Please provide details about your school's needs and how we can help..."
          validation={{
            required: "Message is required",
            minLength: {
              value: 10,
              message: "Message must be at least 10 characters",
            },
          }}
          inputProps={{
            rows: 5,
          }}
        />

        <CheckboxInput
          form={form}
          name="newsletter"
          label="Subscribe to our newsletter for updates on new features and educational resources"
        />
        <SubmitButton
          buttonIcon={Send}
          title="Submit Inquiry"
          loading={isLoading || form.formState.isSubmitting}
          loadingTitle="Submitting..."
        />
      </form>
    </Form>
  );
};

export default ContactForm;
