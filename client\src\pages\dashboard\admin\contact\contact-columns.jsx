import React from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { ImageColumn } from "@/components/data-table/data-table-columns/image-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";

export const ContactColumns = [
  {
    accessorKey: "name",
    header: ({ column }) => <SortableColumn column={column} title="Name" />,
    cell: ({ row }) => (
      <div className="flex items-center space-x-3">
        <ImageColumn
          src={row.original.logo}
          alt={`${row.getValue("name")} logo`}
          fallbackText={row.getValue("name")}
        />
        <div className="flex flex-col">
          <div className="font-medium uppercase">{row.getValue("name")}</div>
          <div className="text-sm text-muted-foreground capitalize">
            {row.original.schoolName}
          </div>
        </div>
      </div>
    ),
    enableSorting: true,
    enableHiding: false,
  },
  {
    accessorKey: "role",
    header: ({ column }) => <SortableColumn column={column} title="Role" />,
    cell: ({ row }) => (
      <div className="text-sm uppercase text-center w-full">
        {row.original.role}
      </div>
    ),
    enableSorting: true,
  },
  {
    accessorKey: "contact",
    header: ({ column }) => <SortableColumn column={column} title="Contact" />,
    cell: ({ row }) => {
      return (
        <div className="space-y-1">
          <div className="text-sm">{row.original.email}</div>
          <div className="text-xs text-muted-foreground">
            {row.original.phone}
          </div>
        </div>
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "inquiryType",
    header: "Inquiry Type",
    cell: ({ row }) => <StatusColumn row={row} statusField="inquiryType" />,
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      return (
        <StatusColumn
          row={row}
          statusField="status"
          variant={{
            resolved: "success",
            new: "destructive",
            contacted: "secondary",
            archived: "warning",
          }}
        />
      );
    },
    enableSorting: true,
  },
  {
    accessorKey: "createdAt",
    header: "Date Created",
    cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
    enableSorting: true,
    sortingFn: "datetime",
  },
];
