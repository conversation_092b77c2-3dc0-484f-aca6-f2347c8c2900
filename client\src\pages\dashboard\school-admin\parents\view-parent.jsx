"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useNavigate } from "react-router-dom";
import { useParent } from "@/context/parent-context";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardHeader, CardContent, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { formatDate } from "@/utils/date-filters";
import {
  User,
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  Briefcase,
  FileText,
  Users,
  Settings,
  MoreVertical,
  Phone,
  Mail,
  Building,
  GraduationCap,
  Heart,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
  Copy,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import { ProfileBanner } from "@/components/dashboard/profile-banner";
import { Separator } from "@/components/ui/separator";

const ViewParent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchParentById, removeParent, updateStatus } = useParent();
  const [parent, setParent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchParent = async () => {
      try {
        setIsLoading(true);
        const data = await fetchParentById(id);
        setParent(data);
      } catch (error) {
        console.error("Failed to fetch parent:", error);
        toast.error("Error", {
          description: error.message || "Failed to load parent details",
        });
        navigate("/dashboard/parents");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchParent();
    } else {
      console.error("No parent ID provided");
      setIsLoading(false);
    }
  }, []);

  const handleDelete = async () => {
    try {
      await removeParent(parent._id);
      toast.success("Parent deleted successfully", {
        description: `${parent.firstName} ${parent.lastName} has been removed.`,
      });
      navigate("/dashboard/parents");
    } catch (error) {
      console.error("Failed to delete parent:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete parent",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = parent.status === "active" ? "inactive" : "active";
      await updateStatus(parent._id, { status: newStatus });
      setOpenStatusChange(false);
      setParent((prevParent) => ({
        ...prevParent,
        status: newStatus,
      }));
      toast.success("Status updated successfully", {
        description: `${parent.firstName} ${parent.lastName}'s status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update parent status",
      });
    }
  };

  if (isLoading) {
    return <ParentDetailSkeleton />;
  }

  if (!parent) {
    return (
      <div className="min-h-screen bg-background">
        <Container className="py-12">
          <PageHeader
            title="Parent Not Found"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Parents", href: "/dashboard/parents" },
              { label: "Not Found" },
            ]}
            actions={[
              {
                label: "Back to Parents",
                icon: ArrowLeft,
                href: "/dashboard/parents",
              },
            ]}
          />
          <Card className="border shadow-lg">
            <CardContent className="p-12">
              <div className="text-center">
                <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-muted mb-6">
                  <User className="h-10 w-10 text-muted-foreground" />
                </div>
                <h3 className="text-2xl font-semibold text-foreground mb-2">
                  Parent not found
                </h3>
                <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                  The parent you're looking for doesn't exist or has been
                  removed from the system.
                </p>
                <Button
                  size="lg"
                  onClick={() => navigate("/dashboard/parents")}
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Parents
                </Button>
              </div>
            </CardContent>
          </Card>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Container className="py-8">
        <div className="space-y-6">
          <PageHeader
            title="Parent Profile"
            breadcrumbs={[
              { label: "Dashboard", href: "/dashboard" },
              { label: "Parents", href: "/dashboard/parents" },
              { label: `${parent.firstName} ${parent.lastName}` },
            ]}
            actions={[
              {
                label: "Edit Profile",
                icon: Edit,
                href: `/dashboard/parents/${parent._id}/edit`,
              },
              {
                label: "Back to Parents",
                icon: ArrowLeft,
                href: "/dashboard/parents",
                variant: "outline",
              },
            ]}
            isLoading={isLoading}
          />

          <ProfileBanner
            parent={parent}
            loading={isLoading}
            setOpenStatusChange={setOpenStatusChange}
            setOpenDelete={setOpenDelete}
          />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-1">
              <Card className="shadow-md">
                <CardHeader className="flex flex-col sm:flex-row items-center sm:items-start gap-4">
                  <Avatar className="h-20 w-20 sm:h-24 sm:w-24 border-4 shadow-lg">
                    <AvatarImage
                      src="/placeholder.svg"
                      alt={`${parent?.firstName} ${parent?.lastName}`}
                    />
                    <AvatarFallback className="text-xl sm:text-2xl font-bold bg-primary text-primary-foreground">
                      {parent?.firstName?.charAt(0) || ""}
                      {parent?.lastName?.charAt(0) || ""}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex flex-col items-center sm:items-start gap-2">
                    <CardTitle className="text-center sm:text-left">
                      {parent?.firstName || "Loading..."}{" "}
                      {parent?.lastName || ""}
                    </CardTitle>
                    <span className="text-xs text-muted-foreground font-mono break-all">
                      ID: {parent?._id || "Loading..."}
                    </span>
                    <Badge
                      variant={
                        parent?.status === "active" ? "default" : "secondary"
                      }
                      className={`px-3 py-1 w-fit ${
                        parent?.status === "active"
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      {parent?.status === "active" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {parent?.status
                        ? parent.status.charAt(0).toUpperCase() +
                          parent.status.slice(1)
                        : "Unknown"}
                    </Badge>
                  </div>
                </CardHeader>

                <Separator />
                <CardContent>
                  <div>
                    <DataField label="Role" value={parent.role} />
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <DataField label="Email" value={parent.email} />
                    <DataField label="Phone" value={parent.phone} />
                  </div>
                </CardContent>
              </Card>
            </div>
            <div className="md:col-span-2 rounded-xl">
              <Tabs defaultValue="tab1" className="w-full">
                <TabsList className="w-full flex justify-start md:justify-center mb-4">
                  <TabsTrigger value="tab1">Tab One</TabsTrigger>
                  <TabsTrigger value="children">
                    <Users className="h-4 w-4" />
                    <span className="hidden sm:inline">Children</span>
                    {parent.children?.length > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-1 h-5 w-5 p-0 text-xs"
                      >
                        {parent.children.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="system">System</TabsTrigger>
                </TabsList>

                <TabsContent value="tab1">
                  <div className=" rounded-lg p-4 shadow">
                    Content for Tab One
                  </div>
                </TabsContent>
                {/* Children Tab */}
                <TabsContent value="children" className="space-y-6">
                  <InfoCard
                    icon={Users}
                    title={`Children (${parent.children?.length || 0})`}
                  >
                    {parent.children && parent.children.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                        {parent.children.map((child, index) => (
                          <Card
                            key={child._id || index}
                            className="border hover:border-primary/50 transition-colors"
                          >
                            <CardContent className="p-4">
                              <div className="flex items-center space-x-3">
                                <Avatar className="h-12 w-12">
                                  <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                    {child.name?.charAt(0) || "?"}
                                  </AvatarFallback>
                                </Avatar>
                                <div className="flex-1 min-w-0">
                                  <p className="font-semibold text-foreground truncate">
                                    {child.name || "Name not available"}
                                  </p>
                                  <p className="text-sm text-muted-foreground">
                                    {child.class || "Class not available"}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted mb-4">
                          <Users className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h3 className="text-lg font-medium text-foreground mb-2">
                          No children registered
                        </h3>
                        <p className="text-muted-foreground">
                          This parent has no children associated with their
                          account.
                        </p>
                      </div>
                    )}
                  </InfoCard>
                </TabsContent>
                {/* System Information Tab */}
                <TabsContent value="system" className="space-y-6">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <InfoCard icon={Clock} title="Timestamps">
                      <div className="space-y-4">
                        <DataField
                          label="Created At"
                          value={formatDate(parent.createdAt)}
                        />
                        <DataField
                          label="Last Updated"
                          value={formatDate(parent.updatedAt)}
                        />
                      </div>
                    </InfoCard>

                    <InfoCard icon={Settings} title="System Details">
                      <div className="space-y-4">
                        <DataField
                          label="Parent ID"
                          value={parent._id}
                          copyable
                        />
                        <div className="space-y-1">
                          <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                            Status
                          </label>
                          <Badge
                            variant={
                              parent.status === "active"
                                ? "default"
                                : "secondary"
                            }
                            className={`w-fit ${
                              parent.status === "active"
                                ? "bg-primary text-primary-foreground"
                                : "bg-secondary text-secondary-foreground"
                            }`}
                          >
                            {parent.status === "active" ? (
                              <CheckCircle className="h-3 w-3 mr-1" />
                            ) : (
                              <XCircle className="h-3 w-3 mr-1" />
                            )}
                            {parent.status.charAt(0).toUpperCase() +
                              parent.status.slice(1)}
                          </Badge>
                        </div>
                      </div>
                    </InfoCard>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          </div>

          {/* Tabs Content */}
          <Tabs defaultValue="personal" className="space-y-4">
            <div className="bg-card rounded-lg border shadow-sm p-1">
              <TabsList className="grid w-full grid-cols-5 bg-transparent">
                <TabsTrigger value="personal">
                  <User className="h-4 w-4" />
                  <span className="hidden sm:inline">Personal</span>
                </TabsTrigger>
                <TabsTrigger value="professional">
                  <Briefcase className="h-4 w-4" />
                  <span className="hidden sm:inline">Professional</span>
                </TabsTrigger>
                <TabsTrigger value="additional">
                  <FileText className="h-4 w-4" />
                  <span className="hidden sm:inline">Additional</span>
                </TabsTrigger>
                <TabsTrigger value="children">
                  <Users className="h-4 w-4" />
                  <span className="hidden sm:inline">Children</span>
                  {parent.children?.length > 0 && (
                    <Badge
                      variant="secondary"
                      className="ml-1 h-5 w-5 p-0 text-xs"
                    >
                      {parent.children.length}
                    </Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="system">
                  <Settings className="h-4 w-4" />
                  <span className="hidden sm:inline">System</span>
                </TabsTrigger>
              </TabsList>
            </div>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <InfoCard icon={User} title="Personal Details">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <DataField label="Gender" value={parent.gender} />
                    <DataField
                      label="Date of Birth"
                      value={
                        parent.dateOfBirth
                          ? formatDate(parent.dateOfBirth)
                          : null
                      }
                    />
                    <DataField
                      label="Marital Status"
                      value={parent.maritalStatus}
                    />
                    <DataField
                      label="Blood Group"
                      value={parent.bloodGroup}
                      icon={Heart}
                    />
                    <DataField label="Nationality" value={parent.nationality} />
                    <DataField label="Religion" value={parent.religion} />
                  </div>
                </InfoCard>

                <InfoCard icon={MapPin} title="Address Information">
                  <div className="space-y-4">
                    <DataField label="Address" value={parent.address} />
                    <div className="grid grid-cols-2 gap-4">
                      <DataField label="City" value={parent.city} />
                      <DataField label="State" value={parent.state} />
                      <DataField
                        label="Pincode"
                        value={parent.pincode}
                        copyable
                      />
                      <DataField label="Country" value={parent.country} />
                    </div>
                  </div>
                </InfoCard>

                <InfoCard
                  icon={Shield}
                  title="Identity Documents"
                  className="lg:col-span-2"
                >
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <DataField
                      label="Aadhar Number"
                      value={parent.aadharNumber}
                      copyable
                    />
                    <DataField
                      label="PAN Number"
                      value={parent.panNumber}
                      copyable
                    />
                  </div>
                </InfoCard>
              </div>
            </TabsContent>

            {/* Professional Information Tab */}
            <TabsContent value="professional" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <InfoCard icon={Briefcase} title="Employment Details">
                  <div className="space-y-4">
                    <DataField label="Occupation" value={parent.occupation} />
                    <DataField
                      label="Company Name"
                      value={parent.companyName}
                      icon={Building}
                    />
                    <DataField label="Designation" value={parent.designation} />
                    <DataField
                      label="Work Phone"
                      value={parent.workPhone}
                      icon={Phone}
                      copyable
                    />
                  </div>
                </InfoCard>

                <InfoCard icon={GraduationCap} title="Education & Income">
                  <div className="space-y-4">
                    <DataField
                      label="Qualification"
                      value={parent.qualification}
                    />
                    <DataField
                      label="Annual Income"
                      value={parent.annualIncome}
                    />
                  </div>
                </InfoCard>

                <InfoCard
                  icon={MapPin}
                  title="Work Address"
                  className="lg:col-span-2"
                >
                  <DataField label="Work Address" value={parent.workAddress} />
                </InfoCard>
              </div>
            </TabsContent>

            {/* Additional Information Tab */}
            <TabsContent value="additional" className="space-y-6">
              <div className="grid grid-cols-1 gap-6">
                <InfoCard icon={Heart} title="Medical Information">
                  <DataField
                    label="Medical Conditions"
                    value={
                      parent.medicalConditions ||
                      "No medical conditions reported"
                    }
                  />
                </InfoCard>

                <InfoCard icon={FileText} title="Additional Notes">
                  <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                    <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                      {parent.notes || "No additional notes available."}
                    </p>
                  </div>
                </InfoCard>
              </div>
            </TabsContent>

            {/* Children Tab */}
            <TabsContent value="children" className="space-y-6">
              <InfoCard
                icon={Users}
                title={`Children (${parent.children?.length || 0})`}
              >
                {parent.children && parent.children.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                    {parent.children.map((child, index) => (
                      <Card
                        key={child._id || index}
                        className="border hover:border-primary/50 transition-colors"
                      >
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12">
                              <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                {child.name?.charAt(0) || "?"}
                              </AvatarFallback>
                            </Avatar>
                            <div className="flex-1 min-w-0">
                              <p className="font-semibold text-foreground truncate">
                                {child.name || "Name not available"}
                              </p>
                              <p className="text-sm text-muted-foreground">
                                {child.class || "Class not available"}
                              </p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted mb-4">
                      <Users className="h-8 w-8 text-muted-foreground" />
                    </div>
                    <h3 className="text-lg font-medium text-foreground mb-2">
                      No children registered
                    </h3>
                    <p className="text-muted-foreground">
                      This parent has no children associated with their account.
                    </p>
                  </div>
                )}
              </InfoCard>
            </TabsContent>

            {/* System Information Tab */}
            <TabsContent value="system" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <InfoCard icon={Clock} title="Timestamps">
                  <div className="space-y-4">
                    <DataField
                      label="Created At"
                      value={formatDate(parent.createdAt)}
                    />
                    <DataField
                      label="Last Updated"
                      value={formatDate(parent.updatedAt)}
                    />
                  </div>
                </InfoCard>

                <InfoCard icon={Settings} title="System Details">
                  <div className="space-y-4">
                    <DataField label="Parent ID" value={parent._id} copyable />
                    <div className="space-y-1">
                      <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                        Status
                      </label>
                      <Badge
                        variant={
                          parent.status === "active" ? "default" : "secondary"
                        }
                        className={`w-fit ${
                          parent.status === "active"
                            ? "bg-primary text-primary-foreground"
                            : "bg-secondary text-secondary-foreground"
                        }`}
                      >
                        {parent.status === "active" ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <XCircle className="h-3 w-3 mr-1" />
                        )}
                        {parent.status.charAt(0).toUpperCase() +
                          parent.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                </InfoCard>
              </div>
            </TabsContent>
          </Tabs>
        </div>

        {/* Delete Confirmation Dialog */}
        <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                <Trash2 className="h-5 w-5 text-destructive" />
                Delete {parent.firstName} {parent.lastName}?
              </AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the
                parent's profile and remove all associated data from our
                servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={handleDelete}
                className="bg-red-600 text-white hover:bg-red-700"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Parent
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

        {/* Status Change Confirmation Dialog */}
        <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle className="flex items-center gap-2">
                {parent.status === "active" ? (
                  <XCircle className="h-5 w-5 text-muted-foreground" />
                ) : (
                  <CheckCircle className="h-5 w-5 text-primary" />
                )}
                {parent.status === "active" ? "Deactivate" : "Activate"}{" "}
                {parent.firstName} {parent.lastName}?
              </AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to{" "}
                {parent.status === "active" ? "deactivate" : "activate"} this
                parent's account?
                {parent.status === "active"
                  ? " They will lose access to the system until reactivated."
                  : " They will regain full access to the system."}
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleStatusChange}>
                {parent.status === "active" ? "Deactivate" : "Activate"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </Container>
    </div>
  );
};

// Professional Skeleton loader
const ParentDetailSkeleton = () => {
  return (
    <div className="min-h-screen bg-background">
      <Container className="py-8">
        <div className="space-y-8">
          {/* Header Skeleton */}
          <div className="flex justify-between items-center">
            <div className="space-y-2">
              <Skeleton className="h-8 w-64" />
              <Skeleton className="h-4 w-96" />
            </div>
            <div className="flex space-x-2">
              <Skeleton className="h-10 w-32" />
              <Skeleton className="h-10 w-32" />
            </div>
          </div>

          {/* Profile Header Skeleton */}
          <Card className="shadow-lg">
            <div className="p-8">
              <div className="flex items-start space-x-6">
                <Skeleton className="h-24 w-24 rounded-full" />
                <div className="space-y-3 flex-1">
                  <Skeleton className="h-8 w-64" />
                  <Skeleton className="h-4 w-96" />
                  <div className="flex space-x-3">
                    <Skeleton className="h-6 w-20" />
                    <Skeleton className="h-6 w-32" />
                  </div>
                </div>
              </div>
            </div>
          </Card>

          {/* Cards Skeleton */}
          <div className="space-y-6">
            <Skeleton className="h-12 w-full" />
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {[1, 2, 3, 4].map((i) => (
                <Card key={i} className="shadow-sm">
                  <CardHeader>
                    <Skeleton className="h-6 w-48" />
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {[1, 2, 3].map((j) => (
                      <div key={j} className="space-y-1">
                        <Skeleton className="h-3 w-24" />
                        <Skeleton className="h-5 w-full" />
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
};

export default ViewParent;
