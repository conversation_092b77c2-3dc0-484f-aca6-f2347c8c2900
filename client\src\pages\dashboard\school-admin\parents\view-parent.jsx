import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useParent } from "@/context/parent-context";
import { PageHeader } from "@/components/dashboard/page-header";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";
import { formatDate } from "@/utils/date-filters";
import {
  User,
  ArrowLeft,
  Edit,
  Trash2,
  MapPin,
  Briefcase,
  FileText,
  Users,
  Settings,
  Phone,
  Mail,
  Building,
  GraduationCap,
  Heart,
  Shield,
  Clock,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import { InfoCard } from "@/components/dashboard/info-card";
import { DataField } from "@/components/dashboard/data-field";
import { ProfileBanner } from "@/components/dashboard/profile-banner";
import { ViewDetailSkeleton } from "@/components/dashboard/view-detail-skeleton";

const ViewParent = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchParentById, removeParent, updateStatus } = useParent();
  const [parent, setParent] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [openDelete, setOpenDelete] = useState(false);
  const [openStatusChange, setOpenStatusChange] = useState(false);

  useEffect(() => {
    const fetchParent = async () => {
      try {
        setIsLoading(true);
        const data = await fetchParentById(id);
        setParent(data);
      } catch (error) {
        console.error("Failed to fetch parent:", error);
        toast.error("Error", {
          description: error.message || "Failed to load parent details",
        });
        navigate("/dashboard/parents");
      } finally {
        setIsLoading(false);
      }
    };

    if (id) {
      fetchParent();
    } else {
      console.error("No parent ID provided");
      setIsLoading(false);
    }
  }, []);

  const handleDelete = async () => {
    try {
      await removeParent(parent._id);
      toast.success("Parent deleted successfully", {
        description: `${parent.firstName} ${parent.lastName} has been removed.`,
      });
      navigate("/dashboard/parents");
    } catch (error) {
      console.error("Failed to delete parent:", error);
      toast.error("Delete Failed", {
        description: error.message || "Failed to delete parent",
      });
    }
  };

  const handleStatusChange = async () => {
    try {
      const newStatus = parent.status === "active" ? "inactive" : "active";
      await updateStatus(parent._id, { status: newStatus });
      setOpenStatusChange(false);
      setParent((prevParent) => ({
        ...prevParent,
        status: newStatus,
      }));
      toast.success("Status updated successfully", {
        description: `${parent.firstName} ${parent.lastName}'s status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error("Failed to update status:", error);
      toast.error("Update Failed", {
        description: error.message || "Failed to update parent status",
      });
    }
  };

  if (isLoading) {
    return <ViewDetailSkeleton />;
  }

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          title="Parent Profile"
          isLoading={isLoading}
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Parents", href: "/dashboard/parents" },
            { label: `${parent.firstName} ${parent.lastName}` },
          ]}
          actions={[
            {
              label: "Edit Profile",
              icon: Edit,
              href: `/dashboard/parents/${parent._id}/edit`,
            },
            {
              label: "Back to Parents",
              icon: ArrowLeft,
              href: "/dashboard/parents",
              variant: "outline",
            },
          ]}
        />

        <ProfileBanner
          data={parent}
          loading={isLoading}
          setOpenStatusChange={setOpenStatusChange}
          setOpenDelete={setOpenDelete}
        />

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1">
            <div className="grid grid-cols-1 gap-6">
              <InfoCard
                icon={User}
                title={`${parent.title} ${parent.firstName} ${parent.lastName}`}
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="System Email"
                    value={parent.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Personal Email"
                    value={parent.personalEmail}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Phone"
                    value={parent.phone}
                    icon={Phone}
                    copyable
                  />
                  {parent.alternatePhone && (
                    <DataField
                      label="Alternate Phone"
                      value={parent.alternatePhone}
                      icon={Phone}
                      copyable
                    />
                  )}
                  <DataField
                    label="Preferred Contact"
                    value={
                      parent.preferredContactMethod?.charAt(0).toUpperCase() +
                      parent.preferredContactMethod?.slice(1)
                    }
                  />
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="Identity Documents"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="Aadhar Number"
                    value={parent.aadharNumber}
                    copyable
                  />
                  <DataField
                    label="PAN Number"
                    value={parent.panNumber}
                    copyable
                  />
                  {parent.profilePhoto && (
                    <DataField
                      label="Profile Photo"
                      value={parent.profilePhoto}
                    />
                  )}
                </div>
              </InfoCard>

              <InfoCard
                icon={Shield}
                title="System Access"
                isLoading={isLoading}
              >
                <div className="space-y-4">
                  <DataField
                    label="System Email"
                    value={parent.email}
                    icon={Mail}
                    copyable
                  />
                  <DataField
                    label="Account Status"
                    value={parent.isActive ? "Active" : "Inactive"}
                  />
                  <div className="space-y-1">
                    <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                      Status
                    </label>
                    <Badge
                      variant={
                        parent.status === "active" ? "default" : "secondary"
                      }
                      className={`w-fit ${
                        parent.status === "active"
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      {parent.status === "active" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {parent.status?.charAt(0).toUpperCase() +
                        parent.status?.slice(1)}
                    </Badge>
                  </div>
                </div>
              </InfoCard>

              <InfoCard
                icon={FileText}
                title="Additional Notes"
                isLoading={isLoading}
              >
                <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                  <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                    {parent.notes || "No additional notes available."}
                  </p>
                </div>
              </InfoCard>
            </div>
          </div>
          <div className="md:col-span-2 rounded-xl">
            <Tabs defaultValue="personal" className="w-full">
              <div className="mb-4">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="personal">
                    <User className="h-4 w-4" />
                    <span className="hidden sm:inline">Personal</span>
                  </TabsTrigger>
                  <TabsTrigger value="professional">
                    <Briefcase className="h-4 w-4" />
                    <span className="hidden sm:inline">Professional</span>
                  </TabsTrigger>
                  <TabsTrigger value="additional">
                    <FileText className="h-4 w-4" />
                    <span className="hidden sm:inline">Additional</span>
                  </TabsTrigger>
                  <TabsTrigger value="children">
                    <Users className="h-4 w-4" />
                    <span className="hidden sm:inline">Children</span>
                    {parent.children?.length > 0 && (
                      <Badge
                        variant="secondary"
                        className="ml-1 h-5 w-5 p-0 text-xs"
                      >
                        {parent.children.length}
                      </Badge>
                    )}
                  </TabsTrigger>
                  <TabsTrigger value="system">
                    <Settings className="h-4 w-4" />
                    <span className="hidden sm:inline">System</span>
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Personal Information Tab */}
              <TabsContent value="personal" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={User}
                      title="Personal Details"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <DataField label="Title" value={parent.title} />
                        <DataField label="Gender" value={parent.gender} />
                        <DataField
                          label="Date of Birth"
                          value={
                            parent.dateOfBirth
                              ? formatDate(parent.dateOfBirth)
                              : null
                          }
                        />
                        <DataField
                          label="Marital Status"
                          value={parent.maritalStatus}
                        />
                        <DataField
                          label="Blood Group"
                          value={parent.bloodGroup}
                          icon={Heart}
                        />
                        <DataField
                          label="Nationality"
                          value={parent.nationality}
                        />
                        <DataField label="Religion" value={parent.religion} />
                        <DataField
                          label="Primary Parent"
                          value={parent.primaryParent ? "Yes" : "No"}
                        />
                      </div>
                    </InfoCard>
                  </div>

                  <InfoCard
                    icon={Phone}
                    title="Contact Information"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Personal Email"
                        value={parent.personalEmail}
                        icon={Mail}
                        copyable
                      />
                      <DataField
                        label="Phone Number"
                        value={parent.phone}
                        icon={Phone}
                        copyable
                      />
                      {parent.alternatePhone && (
                        <DataField
                          label="Alternate Phone"
                          value={parent.alternatePhone}
                          icon={Phone}
                          copyable
                        />
                      )}
                      <DataField
                        label="Preferred Contact Method"
                        value={
                          parent.preferredContactMethod
                            ?.charAt(0)
                            .toUpperCase() +
                          parent.preferredContactMethod?.slice(1)
                        }
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Shield}
                    title="Identity Documents"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Aadhar Number"
                        value={parent.aadharNumber}
                        copyable
                      />
                      <DataField
                        label="PAN Number"
                        value={parent.panNumber}
                        copyable
                      />
                      {parent.profilePhoto && (
                        <DataField
                          label="Profile Photo"
                          value={parent.profilePhoto}
                        />
                      )}
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={MapPin}
                      title="Address Information"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="space-y-4">
                        <DataField label="Address" value={parent.address} />
                        <div className="grid grid-cols-2 gap-4">
                          <DataField label="City" value={parent.city} />
                          <DataField label="State" value={parent.state} />
                          <DataField
                            label="Pincode"
                            value={parent.pincode}
                            copyable
                          />
                          <DataField label="Country" value={parent.country} />
                        </div>
                      </div>
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>

              {/* Professional Information Tab */}
              <TabsContent value="professional" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Briefcase}
                    title="Employment Details"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField label="Occupation" value={parent.occupation} />
                      <DataField
                        label="Company Name"
                        value={parent.companyName}
                        icon={Building}
                      />
                      <DataField
                        label="Designation"
                        value={parent.designation}
                      />
                      <DataField
                        label="Work Phone"
                        value={parent.workPhone}
                        icon={Phone}
                        copyable
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={GraduationCap}
                    title="Education & Income"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Qualification"
                        value={parent.qualification}
                      />
                      <DataField
                        label="Annual Income"
                        value={
                          parent.annualIncome ? `₹${parent.annualIncome}` : null
                        }
                      />
                    </div>
                  </InfoCard>

                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={MapPin}
                      title="Work Address"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <DataField
                        label="Work Address"
                        value={parent.workAddress}
                      />
                    </InfoCard>
                  </div>

                  {(parent.bankName ||
                    parent.bankAccountNumber ||
                    parent.ifscCode) && (
                    <InfoCard
                      icon={Building}
                      title="Banking Information"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                        <DataField label="Bank Name" value={parent.bankName} />
                        <DataField
                          label="Account Number"
                          value={parent.bankAccountNumber}
                          copyable
                        />
                        <DataField
                          label="IFSC Code"
                          value={parent.ifscCode}
                          copyable
                        />
                      </div>
                    </InfoCard>
                  )}
                </div>
              </TabsContent>

              {/* Additional Information Tab */}
              <TabsContent value="additional" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Shield}
                    title="System Access"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="System Email"
                        value={parent.email}
                        icon={Mail}
                        copyable
                      />
                      <DataField
                        label="Account Status"
                        value={parent.isActive ? "Active" : "Inactive"}
                      />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                          Status
                        </label>
                        <Badge
                          variant={
                            parent.status === "active" ? "default" : "secondary"
                          }
                          className={`w-fit ${
                            parent.status === "active"
                              ? "bg-primary text-primary-foreground"
                              : "bg-secondary text-secondary-foreground"
                          }`}
                        >
                          {parent.status === "active" ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          {parent.status?.charAt(0).toUpperCase() +
                            parent.status?.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={FileText}
                    title="Additional Notes"
                    isLoading={isLoading}
                  >
                    <div className="bg-muted/50 rounded-lg p-4 min-h-[120px]">
                      <p className="text-foreground whitespace-pre-wrap leading-relaxed">
                        {parent.notes || "No additional notes available."}
                      </p>
                    </div>
                  </InfoCard>
                </div>
              </TabsContent>

              {/* Children Tab */}
              <TabsContent value="children" className="space-y-6">
                <InfoCard
                  icon={Users}
                  title={`Children (${parent.children?.length || 0})`}
                  isLoading={isLoading}
                >
                  {parent.children && parent.children.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                      {parent.children.map((child, index) => (
                        <Card
                          key={child._id || index}
                          className="border hover:border-primary/50 transition-colors"
                        >
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-12 w-12">
                                <AvatarFallback className="bg-primary/10 text-primary font-semibold">
                                  {child.name?.charAt(0) || "?"}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1 min-w-0">
                                <p className="font-semibold text-foreground truncate">
                                  {child.name || "Name not available"}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                  {child.class || "Class not available"}
                                </p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-muted mb-4">
                        <Users className="h-8 w-8 text-muted-foreground" />
                      </div>
                      <h3 className="text-lg font-medium text-foreground mb-2">
                        No children registered
                      </h3>
                      <p className="text-muted-foreground">
                        This parent has no children associated with their
                        account.
                      </p>
                    </div>
                  )}
                </InfoCard>
              </TabsContent>

              {/* System Information Tab */}
              <TabsContent value="system" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <InfoCard
                    icon={Clock}
                    title="Timestamps"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Created At"
                        value={formatDate(parent.createdAt)}
                      />
                      <DataField
                        label="Last Updated"
                        value={formatDate(parent.updatedAt)}
                      />
                    </div>
                  </InfoCard>

                  <InfoCard
                    icon={Settings}
                    title="System Details"
                    isLoading={isLoading}
                  >
                    <div className="space-y-4">
                      <DataField
                        label="Parent ID"
                        value={parent._id}
                        copyable
                      />
                      <DataField
                        label="System Email"
                        value={parent.email}
                        copyable
                      />
                      <div className="space-y-1">
                        <label className="text-xs font-medium text-muted-foreground uppercase tracking-wide pr-2">
                          Status
                        </label>
                        <Badge
                          variant={
                            parent.status === "active" ? "default" : "secondary"
                          }
                          className={`w-fit ${
                            parent.status === "active"
                              ? "bg-primary text-primary-foreground"
                              : "bg-secondary text-secondary-foreground"
                          }`}
                        >
                          {parent.status === "active" ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          {parent.status?.charAt(0).toUpperCase() +
                            parent.status?.slice(1)}
                        </Badge>
                      </div>
                    </div>
                  </InfoCard>

                  {parent.children && parent.children.length > 0 && (
                    <InfoCard
                      icon={Users}
                      title="Children Summary"
                      isLoading={isLoading}
                      className="lg:col-span-2"
                    >
                      <div className="space-y-2">
                        <DataField
                          label="Total Children"
                          value={parent.children.length.toString()}
                        />
                        <div className="text-sm text-muted-foreground">
                          Children IDs:{" "}
                          {parent.children
                            .map((child) => child._id || child)
                            .join(", ")}
                        </div>
                      </div>
                    </InfoCard>
                  )}
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={openDelete} onOpenChange={setOpenDelete}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-destructive" />
              Delete {parent.firstName} {parent.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              parent's profile and remove all associated data from our servers.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive hover:bg-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Parent
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Status Change Confirmation Dialog */}
      <AlertDialog open={openStatusChange} onOpenChange={setOpenStatusChange}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              {parent.status === "active" ? (
                <XCircle className="h-5 w-5 text-muted-foreground" />
              ) : (
                <CheckCircle className="h-5 w-5 text-primary" />
              )}
              {parent.status === "active" ? "Deactivate" : "Activate"}{" "}
              {parent.firstName} {parent.lastName}?
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to{" "}
              {parent.status === "active" ? "deactivate" : "activate"} this
              parent's account?
              {parent.status === "active"
                ? " They will lose access to the system until reactivated."
                : " They will regain full access to the system."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleStatusChange}>
              {parent.status === "active" ? "Deactivate" : "Activate"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Container>
  );
};

export default ViewParent;
