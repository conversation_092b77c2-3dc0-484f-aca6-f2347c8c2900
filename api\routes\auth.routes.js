// routes/auth.routes.js
import express from "express";
import {
  signUp,
  signIn,
  signOut,
  getProfile,
} from "../controllers/auth.controller.js";
import { protect } from "../middleware/auth.middleware.js";

const router = express.Router();

router.post("/signup", signUp);
router.post("/signin", signIn);
router.post("/signout", signOut);
router.get("/profile", protect, getProfile);

export default router;
