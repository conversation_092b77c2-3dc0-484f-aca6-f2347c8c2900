# School Track Information Overview

## Repository Summary

School Track is a full-stack web application for school management, featuring a React frontend and Node.js backend. The system manages schools, administrators, parents, contacts, and notifications through a comprehensive dashboard interface.

## Repository Structure

- **api/**: Backend Node.js Express server
  - **config/**: Database configuration (MongoDB)
  - **controllers/**: API endpoint controllers
  - **middleware/**: Authentication middleware
  - **models/**: Mongoose data models
  - **routes/**: API route definitions
- **client/**: Frontend React application
  - **src/**: Source code
    - **api/**: API client functions
    - **components/**: Reusable UI components
    - **context/**: React context providers
    - **hooks/**: Custom React hooks
    - **pages/**: Application pages
    - **utils/**: Utility functions

## Projects

### Backend API

**Configuration File**: api/package.json

#### Language & Runtime

**Language**: JavaScript (Node.js)
**Version**: ES Modules (type: "module")
**Package Manager**: npm

#### Dependencies

**Main Dependencies**:

- express: ^4.21.2
- mongoose: ^8.9.0
- jsonwebtoken: ^9.0.2
- bcryptjs: ^2.4.3
- cors: ^2.8.5
- dotenv: ^16.4.7

#### Build & Installation

```bash
cd api
npm install
npm run dev  # Development with nodemon
npm start    # Production
```

#### Main Files

**Entry Point**: api/index.js
**Database**: MongoDB (configured in api/config/db.js)
**Models**: User, School, SchoolAdmin, Parent, Contact, Notification
**Routes**: Authentication, schools, school admins, parents, contacts, notifications, dashboard

### Frontend Client

**Configuration File**: client/package.json

#### Language & Runtime

**Language**: JavaScript (React)
**Version**: React 19
**Build System**: Vite 6.3.1
**Package Manager**: npm

#### Dependencies

**Main Dependencies**:

- react: ^19.0.0
- react-dom: ^19.0.0
- react-router-dom: ^7.6.0
- axios: ^1.9.0
- tailwindcss: ^4.1.5
- @radix-ui components (UI component library)
- zod: ^3.25.56 (validation)
- react-hook-form: ^7.56.1

**Development Dependencies**:

- vite: ^6.3.1
- eslint: ^9.22.0
- @vitejs/plugin-react: ^4.3.4

#### Build & Installation

```bash
cd client
npm install
npm run dev      # Development server
npm run build    # Production build
npm run preview  # Preview production build
```

#### Main Files

**Entry Point**: client/src/main.jsx
**App Component**: client/src/App.jsx

### Parent Management Module

**Backend Components**:

- **Model**: api/models/parent.model.js (Mongoose schema with personal, address, professional information)
- **Controller**: api/controllers/parent.controller.js (CRUD operations for parent records)
- **Routes**: api/routes/parent.routes.js (API endpoints with authentication middleware)

**Frontend Components**:

- **API Client**: client/src/api/parent-api.jsx (Axios functions for parent CRUD operations)
- **Context**: client/src/context/parent-context.jsx (React context for parent state management)
- **Form**: client/src/components/forms/dashboard/parents/parent-form.jsx (React Hook Form implementation)

**Key Features**:

- Comprehensive parent information management (personal, contact, address, professional details)
- Form validation using React Hook Form
- Status management (active/inactive)
- Primary parent designation
- Document uploads (profile photo)
- Integration with school and student records
