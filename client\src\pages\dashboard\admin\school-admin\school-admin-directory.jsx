import { useEffect } from "react";
import { useSchoolAdmin } from "@/context/school-admin-context";
import { Users, UserCog, PlusCircle, Building2, Briefcase } from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { SchoolAdminColumns } from "@/pages/dashboard/admin/school-admin/school-admin-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const SchoolAdminDirectory = () => {
  const { schoolAdmins, isLoading, fetchAllSchoolAdmins } = useSchoolAdmin();

  useEffect(() => {
    fetchAllSchoolAdmins();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="School Admin Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "School Admins" },
          ]}
          actions={[
            {
              label: "New School Admin",
              icon: PlusCircle,
              href: "/dashboard/school-admins/create",
            },
          ]}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Admins"
            value={schoolAdmins.length}
            description="All registered school admins"
            icon={Users}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Active Admins"
            value={
              schoolAdmins.filter((admin) => admin.status === "active").length
            }
            description="Currently active"
            icon={UserCog}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Recent Additions"
            value={
              schoolAdmins.filter((admin) => {
                const createdAt = new Date(admin.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Added in last 30 days"
            icon={Briefcase}
            isLoading={isLoading}
          />

          <StatCard
            title="Schools Represented"
            value={(() => {
              if (schoolAdmins.length === 0) return 0;
              const uniqueSchools = new Set(
                schoolAdmins.map((admin) => admin.schoolId)
              );
              return uniqueSchools.size;
            })()}
            description="Unique schools with admins"
            icon={Building2}
            isLoading={isLoading}
          />
        </div>
        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={schoolAdmins}
              columns={SchoolAdminColumns()}
              model="school-admin"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default SchoolAdminDirectory;
