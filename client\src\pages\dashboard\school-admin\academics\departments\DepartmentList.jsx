import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Link } from "react-router-dom";
import {
  Search,
  Users,
  GraduationCap,
  Building,
  ChevronRight,
  Plus,
} from "lucide-react";

export default function DepartmentList({
  searchQuery,
  setSearchQuery,
  departments,
  selectedDepartment,
  onSelect,
  isLoading,
  isMobile = false,
}) {
  const filteredDepartments = departments.filter(
    (dept) =>
      dept.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dept.head.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className={`${isMobile ? "w-full" : "w-80 border-r"} bg-background`}>
        <div className="p-4 space-y-4">
          <Skeleton className="h-10 w-full" />
          {[...Array(6)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-4">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2 mb-2" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-12" />
                  <Skeleton className="h-5 w-16" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`${
        isMobile ? "w-full" : "w-80 border-r"
      } bg-background flex flex-col h-full`}
    >
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-4">
          <Building className="h-5 w-5 text-primary" />
          <h2 className="font-semibold text-lg">Departments</h2>
          <Button asChild size="sm" className="ml-auto gap-2">
            <Link to="/dashboard/academics/departments/create">
              <Plus className="h-4 w-4" />
              New Department
            </Link>
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search departments..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      {/* Department List */}
      <ScrollArea className="md:h-">
        <div className="p-4 space-y-3">
          {filteredDepartments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Building className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No departments found</p>
            </div>
          ) : (
            filteredDepartments.map((department) => (
              <Card
                key={department.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedDepartment?.id === department.id
                    ? "ring-2 ring-primary bg-primary/5"
                    : "hover:bg-muted/50"
                }`}
                onClick={() => onSelect(department)}
              >
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h3 className="font-semibold text-sm mb-1 truncate">
                        {department.name}
                      </h3>
                      <p className="text-xs text-muted-foreground mb-2 truncate">
                        Head: {department.head}
                      </p>

                      <div className="flex flex-wrap gap-1 mb-2">
                        <Badge
                          variant="secondary"
                          className="text-xs px-2 py-0"
                        >
                          <Users className="h-3 w-3 mr-1" />
                          {department.totalTeachers}
                        </Badge>
                        <Badge variant="outline" className="text-xs px-2 py-0">
                          <GraduationCap className="h-3 w-3 mr-1" />
                          {department.totalStudents}
                        </Badge>
                      </div>

                      <div className="flex items-center justify-between">
                        <Badge
                          variant={
                            department.status === "active"
                              ? "default"
                              : "secondary"
                          }
                          className="text-xs"
                        >
                          {department.status}
                        </Badge>
                        <span className="text-xs text-muted-foreground">
                          {department.courses.length} courses
                        </span>
                      </div>
                    </div>

                    <ChevronRight className="h-4 w-4 text-muted-foreground ml-2 flex-shrink-0" />
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </ScrollArea>

      {/* Footer Stats */}
      <div className="p-4 border-t bg-muted/30">
        <div className="text-xs text-muted-foreground text-center">
          {filteredDepartments.length} of {departments.length} departments
        </div>
      </div>
    </div>
  );
}
