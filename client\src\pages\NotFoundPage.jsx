"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { GraduationCap, Home, ArrowLeft } from "lucide-react";
import { Link } from "react-router-dom";

export default function NotFoundPage() {
  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <div className="text-center">
        <div className="flex justify-center mb-8">
          <GraduationCap className="w-16 h-16 text-primary" />
        </div>

        <h1 className="text-4xl font-bold mb-4">404</h1>
        <h2 className="text-xl font-medium text-foreground mb-4">
          Page Not Found
        </h2>
        <p className="text-base text-muted-foreground mb-8">
          The page you're looking for doesn't exist in SchoolTrack.
        </p>

        <div className="flex gap-3 justify-center">
          <Button asChild>
            <Link to="/" className="flex items-center">
              <Home className="w-4 h-4 mr-2" />
              Go to Dashboard
            </Link>
          </Button>

          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="flex items-center"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Go Back
          </Button>
        </div>
      </div>
    </div>
  );
}
