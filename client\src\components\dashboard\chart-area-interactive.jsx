import * as React from "react";
import { <PERSON>, Area<PERSON>hart, CartesianGrid, XAxis } from "recharts";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  Card,
  CardAction,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";

// Default chart configuration
const defaultChartConfig = {
  primary: {
    label: "Primary",
    color: "var(--primary)",
  },
  secondary: {
    label: "Secondary",
    color: "var(--primary)",
  },
};

// Default time range options
const defaultTimeRangeOptions = [
  { value: "7d", label: "Last 7 days" },
  { value: "30d", label: "Last 30 days" },
  { value: "90d", label: "Last 3 months" },
];

// Default sample data for demonstration
const defaultChartData = [
  { date: "2024-06-24", primary: 132, secondary: 180 },
  { date: "2024-06-25", primary: 141, secondary: 190 },
  { date: "2024-06-26", primary: 434, secondary: 380 },
  { date: "2024-06-27", primary: 448, secondary: 490 },
  { date: "2024-06-28", primary: 149, secondary: 200 },
  { date: "2024-06-29", primary: 103, secondary: 160 },
  { date: "2024-06-30", primary: 446, secondary: 400 },
];

export function ChartAreaInteractive({
  title = "Chart Data",
  description = "Data visualization",
  data = defaultChartData,
  config = defaultChartConfig,
  timeRangeOptions = defaultTimeRangeOptions,
  defaultTimeRange = "30d",
  isLoading = false,
  onTimeRangeChange,
  height = "250px",
  showTimeRangeSelector = true,
  dataKeys = ["primary", "secondary"],
  dateKey = "date",
  stackId = "a",
}) {
  const isMobile = useIsMobile();
  const [timeRange, setTimeRange] = React.useState(defaultTimeRange);

  React.useEffect(() => {
    if (isMobile && timeRangeOptions.some((option) => option.value === "7d")) {
      setTimeRange("7d");
    }
  }, [isMobile, timeRangeOptions]);

  // Handle time range change
  const handleTimeRangeChange = (newTimeRange) => {
    setTimeRange(newTimeRange);
    if (onTimeRangeChange) {
      onTimeRangeChange(newTimeRange);
    }
  };

  const filteredData = React.useMemo(() => {
    if (!data || data.length === 0) return [];

    if (!showTimeRangeSelector) return data;

    const currentDate = new Date();
    let daysToSubtract = 30;

    switch (timeRange) {
      case "7d":
        daysToSubtract = 7;
        break;
      case "30d":
        daysToSubtract = 30;
        break;
      case "90d":
        daysToSubtract = 90;
        break;
      default:
        daysToSubtract = 30;
    }

    const startDate = new Date();
    startDate.setDate(currentDate.getDate() - daysToSubtract);

    return data.filter((item) => {
      const itemDate = new Date(item[dateKey]);
      return itemDate >= startDate;
    });
  }, [data, timeRange, showTimeRangeSelector, dateKey]);

  if (isLoading) {
    return (
      <Card className="@container/card">
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>Loading chart data...</CardDescription>
        </CardHeader>
        <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
          <div
            className={`aspect-auto w-full animate-pulse bg-muted rounded-md`}
            style={{ height }}
          />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="@container/card">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>
          <span className="hidden @[540px]/card:block">{description}</span>
          <span className="@[540px]/card:hidden">{description}</span>
        </CardDescription>
        {showTimeRangeSelector && (
          <CardAction>
            <ToggleGroup
              type="single"
              value={timeRange}
              onValueChange={handleTimeRangeChange}
              variant="outline"
              className="hidden *:data-[slot=toggle-group-item]:!px-4 @[767px]/card:flex"
            >
              {timeRangeOptions.map((option) => (
                <ToggleGroupItem key={option.value} value={option.value}>
                  {option.label}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
            <Select value={timeRange} onValueChange={handleTimeRangeChange}>
              <SelectTrigger
                className="flex w-40 **:data-[slot=select-value]:block **:data-[slot=select-value]:truncate @[767px]/card:hidden"
                size="sm"
                aria-label="Select a value"
              >
                <SelectValue
                  placeholder={timeRangeOptions[0]?.label || "Select range"}
                />
              </SelectTrigger>
              <SelectContent className="rounded-xl">
                {timeRangeOptions.map((option) => (
                  <SelectItem
                    key={option.value}
                    value={option.value}
                    className="rounded-lg"
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </CardAction>
        )}
      </CardHeader>
      <CardContent className="px-2 pt-4 sm:px-6 sm:pt-6">
        <ChartContainer
          config={config}
          className="aspect-auto w-full"
          style={{ height }}
        >
          <AreaChart data={filteredData}>
            <defs>
              {dataKeys.map((key, index) => (
                <linearGradient
                  key={key}
                  id={`fill${key}`}
                  x1="0"
                  y1="0"
                  x2="0"
                  y2="1"
                >
                  <stop
                    offset="5%"
                    stopColor={`var(--primary)`}
                    stopOpacity={index === 0 ? 0.8 : 0.6}
                  />
                  <stop
                    offset="95%"
                    stopColor={`var(--primary)`}
                    stopOpacity={0.1}
                  />
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid vertical={false} />
            <XAxis
              dataKey={dateKey}
              tickLine={false}
              axisLine={false}
              tickMargin={8}
              minTickGap={32}
              tickFormatter={(value) => {
                const date = new Date(value);
                return date.toLocaleDateString("en-US", {
                  month: "short",
                  day: "numeric",
                });
              }}
            />
            <ChartTooltip
              cursor={false}
              defaultIndex={isMobile ? -1 : Math.floor(filteredData.length / 2)}
              content={
                <ChartTooltipContent
                  labelFormatter={(value) => {
                    return new Date(value).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    });
                  }}
                  indicator="dot"
                />
              }
            />
            {dataKeys.map((key) => (
              <Area
                key={key}
                dataKey={key}
                type="natural"
                fill={`url(#fill${key})`}
                stroke={`var(--color-${key})`}
                stackId={stackId}
              />
            ))}
          </AreaChart>
        </ChartContainer>
      </CardContent>
    </Card>
  );
}
