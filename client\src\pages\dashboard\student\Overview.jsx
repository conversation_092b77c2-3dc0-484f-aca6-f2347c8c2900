import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Clipboard<PERSON>he<PERSON>, Clock, Award } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";

const StudentOverview = () => {
  const { user, isLoading } = useAuth();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="Student Dashboard"
        subtitle="Track your courses, assignments, and academic progress"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="My Courses"
          value="6"
          description="Enrolled this semester"
          icon={BookOpen}
          isLoading={isLoading}
        />

        <StatCard
          title="Assignments Due"
          value="4"
          description="This week"
          icon={ClipboardCheck}
          isLoading={isLoading}
          trend="negative"
        />

        <StatCard
          title="Next Class"
          value="10:30 AM"
          description="Mathematics - Room 204"
          icon={Clock}
          isLoading={isLoading}
        />

        <StatCard
          title="GPA"
          value="3.8"
          description="Current semester"
          icon={Award}
          isLoading={isLoading}
          trend="positive"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Academic Performance</CardTitle>
            <CardDescription>Your grades across all subjects</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] bg-muted/20 rounded-md flex items-center justify-center">
              Performance Chart
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Upcoming Schedule</CardTitle>
            <CardDescription>Classes for the next 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Mathematics</p>
                  <p className="text-xs text-muted-foreground">
                    Today, 10:30 AM - Room 204
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Physics</p>
                  <p className="text-xs text-muted-foreground">
                    Today, 1:00 PM - Room 301
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Chemistry</p>
                  <p className="text-xs text-muted-foreground">
                    Tomorrow, 9:00 AM - Lab 2
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Biology</p>
                  <p className="text-xs text-muted-foreground">
                    Tomorrow, 11:00 AM - Lab 1
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default StudentOverview;
