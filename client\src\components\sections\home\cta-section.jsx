import { cn } from "@/lib/utils";
import { Section } from "@/components/ui/section";
import { Button } from "@/components/ui/button";
import Glow from "@/components/ui/glow";
import { Calendar } from "lucide-react";
import { Container } from "@/components/ui/container";

export default function CTA({
  title = "Ready to Revolutionize Your School Management?",
  subtitle = "Join thousands of educational institutions worldwide that use School TRACK to streamline operations, enhance learning experiences, and improve educational outcomes",
  className,
  buttons = [
    {
      href: "/schedule-demo",
      text: "Schedule a Demo",
      variant: "default",
      icon: <Calendar className="h-5 w-5 mr-2" />,
    },
    {
      href: "/free-trial",
      text: "Start 30-Day Free Trial",
      variant: "outline",
    },
  ],
}) {
  return (
    <Section className={cn("group relative overflow-hidden", className)}>
      <Container className="flex flex-col items-center gap-8 text-center">
        <h2 className="max-w-[640px] text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
          {title}
          {subtitle && (
            <p className="mt-4 text-lg leading-relaxed text-muted-foreground sm:text-xl">
              {subtitle}
            </p>
          )}
        </h2>
        {buttons !== false && buttons.length > 0 && (
          <div className="flex justify-center gap-4">
            {buttons.map((button, index) => (
              <Button
                key={index}
                variant={button.variant || "default"}
                size="lg"
                asChild
              >
                <a href={button.href}>
                  {button.icon}
                  {button.text}
                  {button.iconRight}
                </a>
              </Button>
            ))}
          </div>
        )}
      </Container>
      <div className="absolute top-0 left-0 h-full w-full translate-y-[1rem] opacity-80 transition-all duration-500 ease-in-out group-hover:translate-y-[-2rem] group-hover:opacity-100">
        <Glow variant="bottom" />
      </div>
    </Section>
  );
}
