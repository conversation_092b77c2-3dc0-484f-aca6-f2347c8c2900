import React from "react";
import { cn } from "@/lib/utils";
import { cva } from "class-variance-authority";

const glowVariants = cva("absolute w-full", {
  variants: {
    variant: {
      top: "top-0",
      above: "-top-[128px]",
      bottom: "bottom-0",
      below: "-bottom-[128px]",
      center: "top-[50%]",
    },
  },
  defaultVariants: {
    variant: "top",
  },
});

function Glow({ className, variant, ...props }) {
  return (
    <div
      data-slot="glow"
      className={cn(glowVariants({ variant }), className)}
      {...props}
    >
      <div
        className={cn(
          "from-primary-foreground/50 to-primary-foreground/0 absolute left-1/2 h-[256px] w-[95%] -translate-x-1/2 scale-[2.5] rounded-[50%] bg-radial from-10% to-60% opacity-20 sm:h-[512px] dark:opacity-100",
          variant === "center" && "-translate-y-1/2"
        )}
      />
      <div
        className={cn(
          "from-primary/30 to-primary-foreground/0 absolute left-1/2 h-[128px] w-[60%] -translate-x-1/2 scale-200 rounded-[50%] bg-radial from-10% to-60% opacity-20 sm:h-[256px] dark:opacity-100",
          variant === "center" && "-translate-y-1/2"
        )}
      />
    </div>
  );
}

export default Glow;
