import School from "../models/school.model.js";
import User from "../models/user.model.js";
import Notification from "../models/notification.model.js";
import Contact from "../models/contact.model.js";

// Get admin dashboard statistics
export const getAdminDashboardStats = async (req, res) => {
  try {
    // Get current date and date 30 days ago for trend calculations
    const currentDate = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(currentDate.getDate() - 30);

    // Total Schools
    const totalSchools = await School.countDocuments();
    const schoolsLastMonth = await School.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Total Users
    const totalUsers = await User.countDocuments();
    const usersLastMonth = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Active Teachers
    const activeTeachers = await User.countDocuments({
      role: "teacher",
      status: "active",
    });
    const teachersLastMonth = await User.countDocuments({
      role: "teacher",
      createdAt: { $gte: thirtyDaysAgo },
    });

    // System Activity (notifications sent in last 30 days)
    const systemActivity = await Notification.countDocuments({
      status: "sent",
      sentAt: { $gte: thirtyDaysAgo },
    });
    const previousMonthActivity = await Notification.countDocuments({
      status: "sent",
      sentAt: {
        $gte: new Date(thirtyDaysAgo.getTime() - 30 * 24 * 60 * 60 * 1000),
        $lt: thirtyDaysAgo,
      },
    });

    // Additional statistics
    const totalNotifications = await Notification.countDocuments();
    const pendingContacts = await Contact.countDocuments({ status: "new" });
    const totalContacts = await Contact.countDocuments();

    // User distribution by role
    const usersByRole = await User.aggregate([
      {
        $group: {
          _id: "$role",
          count: { $sum: 1 },
        },
      },
    ]);

    // Schools by status
    const schoolsByStatus = await School.aggregate([
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
    ]);

    // Recent activity (last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(currentDate.getDate() - 7);

    const recentActivity = {
      newUsers: await User.countDocuments({
        createdAt: { $gte: sevenDaysAgo },
      }),
      newSchools: await School.countDocuments({
        createdAt: { $gte: sevenDaysAgo },
      }),
      sentNotifications: await Notification.countDocuments({
        status: "sent",
        sentAt: { $gte: sevenDaysAgo },
      }),
      newContacts: await Contact.countDocuments({
        createdAt: { $gte: sevenDaysAgo },
      }),
    };

    res.status(200).json({
      success: true,
      data: {
        overview: {
          totalSchools: {
            current: totalSchools,
            lastMonth: schoolsLastMonth,
            trend: schoolsLastMonth > 0 ? "positive" : "neutral",
          },
          totalUsers: {
            current: totalUsers,
            lastMonth: usersLastMonth,
            trend: usersLastMonth > 0 ? "positive" : "neutral",
          },
          activeTeachers: {
            current: activeTeachers,
            lastMonth: teachersLastMonth,
            trend: teachersLastMonth > 0 ? "positive" : "neutral",
          },
          systemActivity: {
            current: systemActivity,
            lastMonth: previousMonthActivity,
            trend:
              systemActivity > previousMonthActivity
                ? "positive"
                : systemActivity < previousMonthActivity
                ? "negative"
                : "neutral",
          },
        },
        additional: {
          totalNotifications,
          pendingContacts,
          totalContacts,
        },
        distribution: {
          usersByRole,
          schoolsByStatus,
        },
        recentActivity,
      },
    });
  } catch (error) {
    console.error("Error fetching admin dashboard stats:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard statistics",
      error: error.message,
    });
  }
};

// Get school admin dashboard statistics
export const getSchoolAdminDashboardStats = async (req, res) => {
  try {
    const schoolId = req.user.schoolId;

    if (!schoolId) {
      return res.status(400).json({
        success: false,
        message: "School ID not found for user",
      });
    }

    const currentDate = new Date();
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(currentDate.getDate() - 30);

    // Students in this school
    const totalStudents = await User.countDocuments({
      role: "student",
      schoolId: schoolId,
    });
    const studentsLastMonth = await User.countDocuments({
      role: "student",
      schoolId: schoolId,
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Teachers in this school
    const totalTeachers = await User.countDocuments({
      role: "teacher",
      schoolId: schoolId,
    });
    const teachersLastMonth = await User.countDocuments({
      role: "teacher",
      schoolId: schoolId,
      createdAt: { $gte: thirtyDaysAgo },
    });

    // Notifications for this school
    const schoolNotifications = await Notification.countDocuments({
      schoolId: schoolId,
    });

    // Recent notifications
    const recentNotifications = await Notification.countDocuments({
      schoolId: schoolId,
      createdAt: { $gte: thirtyDaysAgo },
    });

    res.status(200).json({
      success: true,
      data: {
        overview: {
          totalStudents: {
            current: totalStudents,
            lastMonth: studentsLastMonth,
            trend: studentsLastMonth > 0 ? "positive" : "neutral",
          },
          totalTeachers: {
            current: totalTeachers,
            lastMonth: teachersLastMonth,
            trend: teachersLastMonth > 0 ? "positive" : "neutral",
          },
          totalNotifications: {
            current: schoolNotifications,
            lastMonth: recentNotifications,
            trend: recentNotifications > 0 ? "positive" : "neutral",
          },
        },
      },
    });
  } catch (error) {
    console.error("Error fetching school admin dashboard stats:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch dashboard statistics",
      error: error.message,
    });
  }
};

// Get admin chart data for schools and school admins creation over time
export const getAdminChartData = async (req, res) => {
  try {
    const { timeRange = "30d" } = req.query;

    // Calculate date range
    const currentDate = new Date();
    let daysBack = 30;

    switch (timeRange) {
      case "7d":
        daysBack = 7;
        break;
      case "30d":
        daysBack = 30;
        break;
      case "90d":
        daysBack = 90;
        break;
      default:
        daysBack = 30;
    }

    const startDate = new Date();
    startDate.setDate(currentDate.getDate() - daysBack);

    // Aggregate schools creation by date
    const schoolsData = await School.aggregate([
      {
        $match: {
          createdAt: { $gte: startDate, $lte: currentDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$createdAt",
            },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    // Aggregate school admins creation by date
    const schoolAdminsData = await User.aggregate([
      {
        $match: {
          role: "school-admin",
          createdAt: { $gte: startDate, $lte: currentDate },
        },
      },
      {
        $group: {
          _id: {
            $dateToString: {
              format: "%Y-%m-%d",
              date: "$createdAt",
            },
          },
          count: { $sum: 1 },
        },
      },
      {
        $sort: { _id: 1 },
      },
    ]);

    // Create a complete date range array
    const dateRange = [];
    for (let i = daysBack; i >= 0; i--) {
      const date = new Date();
      date.setDate(currentDate.getDate() - i);
      dateRange.push(date.toISOString().split("T")[0]);
    }

    // Map data to complete date range
    const chartData = dateRange.map((date) => {
      const schoolsCount =
        schoolsData.find((item) => item._id === date)?.count || 0;
      const schoolAdminsCount =
        schoolAdminsData.find((item) => item._id === date)?.count || 0;

      return {
        date,
        schools: schoolsCount,
        schoolAdmins: schoolAdminsCount,
      };
    });

    res.status(200).json({
      success: true,
      data: chartData,
    });
  } catch (error) {
    console.error("Error fetching admin chart data:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch chart data",
      error: error.message,
    });
  }
};
