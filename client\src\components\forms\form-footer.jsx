import React from "react";
import { SubmitButton } from "@/components/form-inputs/submit-button";
import { CloseButton } from "@/components/form-inputs/close-button";

export function FormFooter({ href, editingId, loading, title, parent }) {
  return (
    <div className="flex items-center justify-between md:justify-end gap-4 ">
      <CloseButton href={href} parent={parent} />
      <SubmitButton
        title={editingId ? `Update ${title}` : `Save ${title}`}
        loading={loading}
      />
    </div>
  );
}
