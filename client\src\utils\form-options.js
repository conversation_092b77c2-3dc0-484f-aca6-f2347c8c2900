import {
  AlertCircle,
  Bell,
  CheckCircle,
  Info,
  Library,
  XCircle,
} from "lucide-react";

export const schoolTypes = [
  { label: "Government", value: "government" },
  { label: "Private Aided", value: "private aided" },
  { label: "Private Unaided", value: "private unaided" },
  { label: "Central Government", value: "central government" },
  { label: "Deemed University", value: "deemed university" },
  { label: "International", value: "international" },
  { label: "Other", value: "other" },
];

export const boards = [
  { label: "CBSE Delhi", value: "cbse" },
  { label: "ICSE Board", value: "icse" },
  { label: "State Education Board", value: "state" },
  { label: "Cambridge International", value: "cambridge" },
  { label: "IB Organization", value: "ib" },
  { label: "American Curriculum", value: "american" },
  { label: "Other", value: "other" },
];

export const affiliations = [
  { label: "CBSE", value: "cbse" },
  { label: "CISCE (ICSE/ISC)", value: "icse" },
  { label: "State Board", value: "state" },
  { label: "IB (International Baccalaureate)", value: "ib" },
  { label: "Cambridge International", value: "cambridge" },
  { label: "Other", value: "other" },
];

export const subscriptions = [
  { label: "Basic", value: "basic" },
  { label: "Standard", value: "standard" },
  { label: "Enterprise", value: "enterprise" },
];

export const subscriptionDurations = [
  { label: "1 Month", value: "1 month" },
  { label: "3 Month", value: "3 month" },
  { label: "6 Month", value: "6 month" },
  { label: "Yearly", value: "yearly" },
];

export const paymentMethods = [
  { label: "Credit Card", value: "credit card" },
  { label: "Paypal", value: "paypal" },
];

// school admin
export const adminRoles = [
  { label: "Super Admin", value: "Super Admin" },
  { label: "Principal", value: "Principal" },
  { label: "Vice Principal", value: "Vice Principal" },
  { label: "Admin Officer", value: "Admin Officer" },
  { label: "Accountant", value: "Accountant" },
];

export const departments = [
  { label: "Administration", value: "Administration" },
  { label: "Academic Affairs", value: "Academic Affairs" },
  { label: "Finance & Accounts", value: "Finance & Accounts" },
  { label: "Human Resources", value: "Human Resources" },
  { label: "IT", value: "IT" },
];

export const qualifications = [
  { label: "High School", value: "High School" },
  { label: "Bachelor's Degree", value: "Bachelor's Degree" },
  { label: "Master's Degree", value: "Master's Degree" },
  { label: "B.Ed", value: "B.Ed" },
  { label: "MBA", value: "MBA" },
];

export const employmentTypes = [
  { label: "Full Time", value: "Full Time" },
  { label: "Part Time", value: "Part Time" },
  { label: "Contract", value: "Contract" },
  { label: "Permanent", value: "Permanent" },
];

export const genderOptions = [
  { label: "Male", value: "Male" },
  { label: "Female", value: "Female" },
  { label: "Other", value: "Other" },
];

export const bloodGroups = [
  { label: "A+", value: "A+" },
  { label: "A-", value: "A-" },
  { label: "B+", value: "B+" },
  { label: "B-", value: "B-" },
  { label: "AB+", value: "AB+" },
  { label: "AB-", value: "AB-" },
  { label: "O+", value: "O+" },
  { label: "O-", value: "O-" },
];

export const maritalStatuses = [
  { label: "Single", value: "Single" },
  { label: "Married", value: "Married" },
  { label: "Divorced", value: "Divorced" },
];

// notification
export const recipientOptions = [
  { value: "all-users", label: "All Users" },
  { value: "all-students", label: "All Students" },
  { value: "all-teachers", label: "All Teachers" },
  { value: "all-parents", label: "All Parents" },
  { value: "specific-class", label: "Specific Class" },
  { value: "specific-users", label: "Specific Users" },
];

export const notificationTypes = [
  { label: "Assignment", value: "Assignment", icon: AlertCircle },
  { value: "Exam", label: "Exam Result", icon: CheckCircle },
  { value: "Grade", label: "Grade Update", icon: CheckCircle },
  { value: "Event", label: "School Event", icon: Info },
  { value: "Payment", label: "Payment Reminder", icon: XCircle },
  { value: "Library", label: "Library Notice", icon: Library },
  { value: "General", label: "General Announcement", icon: Bell },
];

export const priorityOptions = [
  { label: "Low Priority", value: "Low" },
  { label: "Medium Priority", value: "Medium" },
  { label: "High Priority", value: "High" },
];

export const classOptions = [
  { label: "Class 1", value: "class1" },
  { label: "Class 2", value: "class2" },
  { label: "Class 3", value: "class3" },
  { label: "Class 4", value: "class4" },
  { label: "Class 5", value: "class5" },
  { label: "Class 6", value: "class6" },
  { label: "Class 7", value: "class7" },
  { label: "Class 8", value: "class8" },
  { label: "Class 9", value: "class9" },
  { label: "Class 10", value: "class10" },
  { label: "Class 11", value: "class11" },
  { label: "Class 12", value: "class12" },
];

// Parent and Student Options
export const relationshipTypes = [
  { label: "Father", value: "Father" },
  { label: "Mother", value: "Mother" },
  { label: "Guardian", value: "Guardian" },
  { label: "Grandfather", value: "Grandfather" },
  { label: "Grandmother", value: "Grandmother" },
  { label: "Uncle", value: "Uncle" },
  { label: "Aunt", value: "Aunt" },
  { label: "Other", value: "Other" },
];

export const occupations = [
  { label: "Business", value: "Business" },
  { label: "Government Service", value: "Government Service" },
  { label: "Private Service", value: "Private Service" },
  { label: "Doctor", value: "Doctor" },
  { label: "Engineer", value: "Engineer" },
  { label: "Teacher", value: "Teacher" },
  { label: "Lawyer", value: "Lawyer" },
  { label: "Farmer", value: "Farmer" },
  { label: "Self Employed", value: "Self Employed" },
  { label: "Homemaker", value: "Homemaker" },
  { label: "Retired", value: "Retired" },
  { label: "Other", value: "Other" },
];

export const incomeRanges = [
  { label: "Below ₹2,00,000", value: "below-200000" },
  { label: "₹2,00,000 - ₹5,00,000", value: "200000-500000" },
  { label: "₹5,00,000 - ₹10,00,000", value: "500000-1000000" },
  { label: "₹10,00,000 - ₹20,00,000", value: "1000000-2000000" },
  { label: "Above ₹20,00,000", value: "above-2000000" },
];

export const admissionTypes = [
  { label: "New Admission", value: "New Admission" },
  { label: "Transfer", value: "Transfer" },
  { label: "Re-admission", value: "Re-admission" },
];

export const studentCategories = [
  { label: "General", value: "General" },
  { label: "OBC", value: "OBC" },
  { label: "SC", value: "SC" },
  { label: "ST", value: "ST" },
  { label: "EWS", value: "EWS" },
];

export const transportModes = [
  { label: "School Bus", value: "School Bus" },
  { label: "Private Vehicle", value: "Private Vehicle" },
  { label: "Walking", value: "Walking" },
  { label: "Public Transport", value: "Public Transport" },
  { label: "Bicycle", value: "Bicycle" },
];

// Teacher Options
export const teachingExperience = [
  { label: "0-1 years", value: "0-1" },
  { label: "1-3 years", value: "1-3" },
  { label: "3-5 years", value: "3-5" },
  { label: "5-10 years", value: "5-10" },
  { label: "10-15 years", value: "10-15" },
  { label: "15+ years", value: "15+" },
];

export const teachingSubjects = [
  { label: "Mathematics", value: "Mathematics" },
  { label: "Science", value: "Science" },
  { label: "Physics", value: "Physics" },
  { label: "Chemistry", value: "Chemistry" },
  { label: "Biology", value: "Biology" },
  { label: "English", value: "English" },
  { label: "Hindi", value: "Hindi" },
  { label: "Social Studies", value: "Social Studies" },
  { label: "History", value: "History" },
  { label: "Geography", value: "Geography" },
  { label: "Computer Science", value: "Computer Science" },
  { label: "Physical Education", value: "Physical Education" },
  { label: "Art", value: "Art" },
  { label: "Music", value: "Music" },
  { label: "Sanskrit", value: "Sanskrit" },
  { label: "Economics", value: "Economics" },
  { label: "Political Science", value: "Political Science" },
  { label: "Commerce", value: "Commerce" },
  { label: "Accountancy", value: "Accountancy" },
];

export const gradeLevels = [
  { label: "Pre-Primary", value: "Pre-Primary" },
  { label: "Primary (1-5)", value: "Primary" },
  { label: "Middle (6-8)", value: "Middle" },
  { label: "Secondary (9-10)", value: "Secondary" },
  { label: "Senior Secondary (11-12)", value: "Senior Secondary" },
];

// Department Options
export const departmentTypes = [
  { label: "Academic", value: "Academic" },
  { label: "Administrative", value: "Administrative" },
  { label: "Support", value: "Support" },
  { label: "Special", value: "Special" },
];

// Terms/Academic Year Options
export const termTypes = [
  { label: "Trimester", value: "Trimester" },
  { label: "Semester", value: "Semester" },
  { label: "Annual", value: "Annual" },
];

export const feeTypes = [
  { label: "Tuition Fee", value: "Tuition Fee" },
  { label: "Development Fee", value: "Development Fee" },
  { label: "Transport Fee", value: "Transport Fee" },
  { label: "Library Fee", value: "Library Fee" },
  { label: "Laboratory Fee", value: "Laboratory Fee" },
  { label: "Sports Fee", value: "Sports Fee" },
  { label: "Examination Fee", value: "Examination Fee" },
  { label: "Other", value: "Other" },
];

// Class/Section Options
export const sectionOptions = [
  { label: "A", value: "A" },
  { label: "B", value: "B" },
  { label: "C", value: "C" },
  { label: "D", value: "D" },
  { label: "E", value: "E" },
];

export const classTypes = [
  { label: "Regular", value: "regular" },
  { label: "Special", value: "special" },
  { label: "Remedial", value: "remedial" },
  { label: "Advanced", value: "advanced" },
];

// Additional options for teacher and subject forms
export const subjectTypes = [
  { label: "Core Subject", value: "core" },
  { label: "Elective Subject", value: "elective" },
  { label: "Language", value: "language" },
  { label: "Science", value: "science" },
  { label: "Mathematics", value: "mathematics" },
  { label: "Social Studies", value: "social" },
  { label: "Arts", value: "arts" },
  { label: "Physical Education", value: "physical" },
  { label: "Vocational", value: "vocational" },
];

export const difficultyLevels = [
  { label: "Beginner", value: "beginner" },
  { label: "Intermediate", value: "intermediate" },
  { label: "Advanced", value: "advanced" },
  { label: "Expert", value: "expert" },
];

export const assessmentTypes = [
  { label: "Written Exam", value: "written" },
  { label: "Practical Exam", value: "practical" },
  { label: "Project", value: "project" },
  { label: "Assignment", value: "assignment" },
  { label: "Presentation", value: "presentation" },
  { label: "Quiz", value: "quiz" },
];

export const titles = [
  { label: "Mr.", value: "Mr." },
  { label: "Mrs.", value: "Mrs." },
  { label: "Ms.", value: "Ms." },
  { label: "Miss", value: "Miss" },
  { label: "Dr.", value: "Dr." },
  { label: "Prof.", value: "Prof." },
  { label: "Rev.", value: "Rev." },
];
