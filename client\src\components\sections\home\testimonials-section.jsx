import { cn } from "@/lib/utils";
import { Container } from "@/components/ui/container";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { StarIcon } from "lucide-react";

export default function Testimonials({
  title = "What Educators Say About Our Demos",
  description = "Hear from educational professionals who have experienced our personalized demonstrations and implemented School TRACK in their institutions.",
  badge = (
    <Announcement>
      <AnnouncementTag>Testimonials</AnnouncementTag>
      <AnnouncementTitle>Success Stories</AnnouncementTitle>
    </Announcement>
  ),
  testimonials = [
    {
      quote:
        "The demo was incredibly helpful in understanding how School TRACK could address our specific administrative challenges. The team was knowledgeable and tailored the presentation to our needs.",
      author: "Dr. <PERSON>",
      role: "Principal, Westlake High School",
      avatar: "/avatars/avatar-1.png",
      rating: 5,
    },
    {
      quote:
        "I was impressed by how comprehensive the demo was. They showed us exactly how the system would work for our unique scheduling requirements and answered all our technical questions.",
      author: "<PERSON>",
      role: "IT Director, Springfield School District",
      avatar: "/avatars/avatar-2.png",
      rating: 5,
    },
    {
      quote:
        "The demo made it clear how much time we could save on attendance tracking and grade management. The presenter understood our pain points and showed us specific solutions.",
      author: "Jennifer Martinez",
      role: "Assistant Principal, Oakridge Academy",
      avatar: "/avatars/avatar-3.png",
      rating: 5,
    },
  ],
  className,
}) {
  return (
    <div className={cn("overflow-hidden py-16 sm:py-24", className)}>
      <Container className="gap-12">
        <div className="flex flex-col items-center gap-6 text-center sm:gap-8">
          {badge}

          <h2 className="animate-appear from-foreground to-foreground dark:to-muted-foreground relative z-10 inline-block bg-linear-to-r bg-clip-text text-3xl leading-tight font-semibold text-balance text-transparent drop-shadow-2xl opacity-0 delay-100 sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
            {title}
          </h2>

          <p className="text-md animate-appear text-muted-foreground relative z-10 max-w-[640px] font-medium text-balance opacity-0 delay-200 sm:text-lg">
            {description}
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="animate-appear mt-12 relative z-10 grid gap-6 opacity-0 delay-300 sm:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="group relative overflow-hidden rounded-2xl border p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg bg-card/50 hover:bg-card/80"
            >
              {/* Rating */}
              <div className="mb-4 flex">
                {Array.from({ length: 5 }).map((_, i) => (
                  <StarIcon
                    key={i}
                    className={cn(
                      "h-4 w-4",
                      i < testimonial.rating
                        ? "fill-primary text-primary"
                        : "fill-muted text-muted"
                    )}
                  />
                ))}
              </div>

              {/* Quote */}
              <blockquote className="mb-6 text-sm text-muted-foreground">
                "{testimonial.quote}"
              </blockquote>

              {/* Author */}
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage
                    src={testimonial.avatar}
                    alt={testimonial.author}
                  />
                  <AvatarFallback>
                    {testimonial.author
                      .split(" ")
                      .map((n) => n[0])
                      .join("")}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium">{testimonial.author}</p>
                  <p className="text-xs text-muted-foreground">
                    {testimonial.role}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="animate-appear mt-16 relative z-10 opacity-0 delay-400">
          <div className="grid gap-8 sm:grid-cols-3">
            <div className="text-center">
              <p className="text-4xl font-bold text-primary mb-2">98%</p>
              <p className="text-sm text-muted-foreground">
                of demo attendees rate our presentations as "highly informative"
              </p>
            </div>
            <div className="text-center">
              <p className="text-4xl font-bold text-primary mb-2">45+</p>
              <p className="text-sm text-muted-foreground">
                demos conducted each month for educational institutions
              </p>
            </div>
            <div className="text-center">
              <p className="text-4xl font-bold text-primary mb-2">92%</p>
              <p className="text-sm text-muted-foreground">
                of demo attendees move forward with implementing School TRACK
              </p>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
