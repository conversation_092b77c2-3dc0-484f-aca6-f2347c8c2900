import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Plus } from "lucide-react";
import { NavUser } from "@/components/sidebar/nav-user";
import { ModeToggle } from "@/components/ui/mode-toggle";

export function SidebarHeader() {
  const [searchQuery, setSearchQuery] = useState("");
  return (
    <div className="flex h-16 items-center gap-4 border-b px-4">
      <SidebarTrigger />
      <div className="flex-1">
        <Input
          placeholder="Search ..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="max-w-sm hidden md:block"
        />
      </div>
      <Button variant="outline" size="icon">
        <Plus className="h-5 w-5" />
        <span className="sr-only">Add new</span>
      </Button>
      <ModeToggle />
      <NavUser />
    </div>
  );
}
