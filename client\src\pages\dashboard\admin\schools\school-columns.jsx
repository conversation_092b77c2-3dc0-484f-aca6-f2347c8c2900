import React, { useState } from "react";
import { StatusColumn } from "@/components/data-table/data-table-columns/status-column";
import { ImageColumn } from "@/components/data-table/data-table-columns/image-column";
import { DateColumn } from "@/components/data-table/data-table-columns/data-columns";
import { SortableColumn } from "@/components/data-table/data-table-columns/sortable-column";
import { ActionColumn } from "@/components/data-table/data-table-columns/action-column";
import { useNavigate } from "react-router-dom";
import { useSchool } from "@/context/school-context";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alert-dialog";

export const SchoolColumns = () => {
  return [
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumn column={column} title="School Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center space-x-3">
          <ImageColumn
            src={row.original.schoolLogo}
            alt={`${row.getValue("name")} logo`}
            fallbackText={row.getValue("name")}
          />
          <div className="flex flex-col">
            <div className="font-medium">{row.getValue("name")}</div>
            <div className="text-sm text-muted-foreground">
              {row.original.registrationNumber}
            </div>
          </div>
        </div>
      ),
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "type",
      header: ({ column }) => <SortableColumn column={column} title="Type" />,
      cell: ({ row }) => (
        <div className="text-sm uppercase text-center w-full">
          {row.original.type}
        </div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "board",
      header: ({ column }) => <SortableColumn column={column} title="Board" />,
      cell: ({ row }) => (
        <div className="text-sm text-center w-full">{row.original.board}</div>
      ),
      enableSorting: true,
    },
    {
      accessorKey: "location",
      header: ({ column }) => (
        <SortableColumn column={column} title="Location" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm">
              {row.original.city}, {row.original.state}
            </div>
            <div className="text-xs text-muted-foreground">
              {row.original.country}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "contact",
      header: ({ column }) => (
        <SortableColumn column={column} title="Contact" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm">{row.original.email}</div>
            <div className="text-xs text-muted-foreground">
              {row.original.phone}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "principal",
      header: ({ column }) => (
        <SortableColumn column={column} title="Principal" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium">
              {row.original.principalName}
            </div>
            <div className="text-xs text-muted-foreground">
              {row.original.principalEmail}
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "students",
      header: ({ column }) => (
        <SortableColumn column={column} title="Students" />
      ),
      cell: ({ row }) => {
        return (
          <div className="space-y-1">
            <div className="text-sm font-medium">
              {row.original.numberOfStudents}
            </div>
            <div className="text-xs text-muted-foreground">
              {row.original.numberOfTeachers} Teachers
            </div>
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "subscriptionPlan",
      header: "Subscription",
      cell: ({ row }) => (
        <StatusColumn
          row={row}
          statusField="subscriptionPlan"
          variant={{
            basic: "secondary",
            standard: "default",
            enterprise: "success",
          }}
        />
      ),
      enableSorting: true,
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => {
        return (
          <StatusColumn
            row={row}
            statusField="status"
            variant={{
              active: "success",
              inactive: "destructive",
              pending: "warning",
              suspended: "destructive",
            }}
          />
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "establishedDate",
      header: "Established",
      cell: ({ row }) => <DateColumn row={row} accessorKey="establishedDate" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      accessorKey: "createdAt",
      header: "Date Added",
      cell: ({ row }) => <DateColumn row={row} accessorKey="createdAt" />,
      enableSorting: true,
      sortingFn: "datetime",
    },
    {
      id: "actions",
      cell: ({ row }) => <SchoolActions row={row} />,
    },
  ];
};

const SchoolActions = ({ row }) => {
  const navigate = useNavigate();
  const { removeSchool } = useSchool();

  const handleView = (e) => {
    navigate(`/dashboard/schools/${row.original._id}`);
  };

  const handleEdit = (e) => {
    navigate(`/dashboard/schools/${row.original._id}/edit`);
  };

  const [open, setOpen] = useState(false);

  const handleDelete = async () => {
    try {
      await removeSchool(row.original._id);
      toast.success(`${row.original.name} deleted successfully.`);
      setOpen(false);
    } catch (error) {
      console.error("Failed to delete school:", error);
    }
  };

  return (
    <>
      <ActionColumn
        row={row}
        onView={handleView}
        onEdit={handleEdit}
        onDelete={() => setOpen(true)}
      />
      <AlertDialog open={open} onOpenChange={setOpen}>
        <AlertDialogTrigger asChild />
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete {row.original.name}?</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this school? This action cannot be
              undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};
