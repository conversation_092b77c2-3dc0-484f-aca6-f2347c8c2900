import SchoolAdmin from "../models/school-admin.model.js";
import User from "../models/user.model.js";
import bcrypt from "bcryptjs";

// Create a new school admin
export const createSchoolAdmin = async (req, res) => {
  try {
    const adminData = req.body;

    // Check if username or email already exists
    const existingUser = await User.findOne({
      $or: [{ email: adminData.email }, { username: adminData.username }],
    });

    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: "User with this email or username already exists",
      });
    }

    // Create user account for the admin
    const hashedPassword = await bcrypt.hash(adminData.password, 12);
    const newUser = new User({
      name: `${adminData.firstName} ${adminData.lastName}`,
      email: adminData.email,
      password: hashedPassword,
      role: "school-admin",
      schoolId: adminData.schoolId,
    });

    const savedUser = await newUser.save();

    // Create school admin with reference to user
    const newSchoolAdmin = new SchoolAdmin({
      ...adminData,
      userId: savedUser._id,
    });

    await newSchoolAdmin.save();

    res.status(201).json({
      success: true,
      message: "School admin created successfully",
      data: newSchoolAdmin,
    });
  } catch (error) {
    console.error("Create School Admin Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `School admin with this ${field} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

// Get all school admins
export const getAllSchoolAdmins = async (req, res) => {
  try {
    const { schoolId } = req.query;

    let query = {};
    if (schoolId) {
      query.schoolId = schoolId;
    }

    const schoolAdmins = await SchoolAdmin.find(query)
      .sort({ createdAt: -1 })
      .populate("schoolId", "name");

    res.status(200).json({
      success: true,
      count: schoolAdmins.length,
      data: schoolAdmins,
    });
  } catch (error) {
    console.error("Get All School Admins Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Get school admin by ID
export const getSchoolAdminById = async (req, res) => {
  try {
    const schoolAdmin = await SchoolAdmin.findById(req.params.id).populate(
      "schoolId",
      "name"
    );

    if (!schoolAdmin) {
      return res.status(404).json({
        success: false,
        message: "School admin not found",
      });
    }

    res.status(200).json({
      success: true,
      data: schoolAdmin,
    });
  } catch (error) {
    console.error("Get School Admin By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Update school admin
export const updateSchoolAdmin = async (req, res) => {
  try {
    const adminData = req.body;

    // Check if updating email or username
    if (adminData.email || adminData.username) {
      const schoolAdmin = await SchoolAdmin.findById(req.params.id);

      if (!schoolAdmin) {
        return res.status(404).json({
          success: false,
          message: "School admin not found",
        });
      }

      // Check if email or username already exists for another user
      if (adminData.email && adminData.email !== schoolAdmin.email) {
        const existingEmail = await SchoolAdmin.findOne({
          email: adminData.email,
          _id: { $ne: req.params.id },
        });

        if (existingEmail) {
          return res.status(400).json({
            success: false,
            message: "Email already in use by another admin",
          });
        }

        // Update email in User model as well
        if (schoolAdmin.userId) {
          await User.findByIdAndUpdate(schoolAdmin.userId, {
            email: adminData.email,
          });
        }
      }

      if (adminData.username && adminData.username !== schoolAdmin.username) {
        const existingUsername = await SchoolAdmin.findOne({
          username: adminData.username,
          _id: { $ne: req.params.id },
        });

        if (existingUsername) {
          return res.status(400).json({
            success: false,
            message: "Username already in use by another admin",
          });
        }
      }
    }

    // Update password if provided
    if (adminData.password) {
      const schoolAdmin = await SchoolAdmin.findById(req.params.id);
      if (schoolAdmin && schoolAdmin.userId) {
        const hashedPassword = await bcrypt.hash(adminData.password, 12);
        await User.findByIdAndUpdate(schoolAdmin.userId, {
          password: hashedPassword,
        });
      }
      delete adminData.password;
    }

    // Update name in User model if first name or last name changed
    if (adminData.firstName || adminData.lastName) {
      const schoolAdmin = await SchoolAdmin.findById(req.params.id);
      if (schoolAdmin && schoolAdmin.userId) {
        const firstName = adminData.firstName || schoolAdmin.firstName;
        const lastName = adminData.lastName || schoolAdmin.lastName;

        await User.findByIdAndUpdate(schoolAdmin.userId, {
          name: `${firstName} ${lastName}`,
        });
      }
    }

    const updatedSchoolAdmin = await SchoolAdmin.findByIdAndUpdate(
      req.params.id,
      adminData,
      {
        new: true,
        runValidators: true,
      }
    );

    if (!updatedSchoolAdmin) {
      return res.status(404).json({
        success: false,
        message: "School admin not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "School admin updated successfully",
      data: updatedSchoolAdmin,
    });
  } catch (error) {
    console.error("Update School Admin Error:", error);

    if (error.name === "ValidationError") {
      const messages = Object.values(error.errors).map((val) => val.message);
      return res.status(400).json({
        success: false,
        message: messages.join(", "),
      });
    }

    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      return res.status(400).json({
        success: false,
        message: `School admin with this ${field} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

// Delete school admin
export const deleteSchoolAdmin = async (req, res) => {
  try {
    const schoolAdmin = await SchoolAdmin.findById(req.params.id);

    if (!schoolAdmin) {
      return res.status(404).json({
        success: false,
        message: "School admin not found",
      });
    }

    // Delete associated user account if exists
    if (schoolAdmin.userId) {
      await User.findByIdAndDelete(schoolAdmin.userId);
    }

    await SchoolAdmin.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "School admin deleted successfully",
    });
  } catch (error) {
    console.error("Delete School Admin Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
