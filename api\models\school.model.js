import mongoose from "mongoose";

const SchoolSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: [true, "School name is required"],
      trim: true,
    },
    registrationNumber: {
      type: String,
      required: [true, "Registration number is required"],
      trim: true,
    },
    type: {
      type: String,
      required: [true, "School type is required"],
      trim: true,
    },
    establishedDate: {
      type: Date,
      required: [true, "Established date is required"],
    },
    board: {
      type: String,
      required: [true, "Board/Curriculum is required"],
      trim: true,
    },
    website: {
      type: String,
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    phone: {
      type: String,
      required: [true, "Phone number is required"],
      trim: true,
    },
    description: {
      type: String,
      required: [true, "Description is required"],
      trim: true,
    },

    // Address Information
    address: {
      type: String,
      required: [true, "School address is required"],
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    state: {
      type: String,
      required: [true, "State is required"],
      trim: true,
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      trim: true,
    },
    postalCode: {
      type: String,
      required: [true, "Postal code is required"],
      trim: true,
    },

    // Administrative Information
    principalName: {
      type: String,
      required: [true, "Principal name is required"],
      trim: true,
    },
    principalEmail: {
      type: String,
      required: [true, "Principal email is required"],
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    principalPhone: {
      type: String,
      required: [true, "Principal phone is required"],
      trim: true,
    },
    affiliationNumber: {
      type: String,
      required: [true, "Affiliation number is required"],
      trim: true,
    },
    affiliationBody: {
      type: String,
      required: [true, "Affiliated body is required"],
      trim: true,
    },
    taxId: {
      type: String,
      required: [true, "Tax ID is required"],
      trim: true,
    },
    vision: {
      type: String,
      required: [true, "Vision is required"],
      trim: true,
    },
    mission: {
      type: String,
      required: [true, "Mission is required"],
      trim: true,
    },

    // Facilities & Features
    numberOfClassrooms: {
      type: Number,
      required: [true, "Number of classrooms is required"],
    },
    numberOfTeachers: {
      type: Number,
      required: [true, "Number of teachers is required"],
    },
    numberOfStudents: {
      type: Number,
      required: [true, "Number of students is required"],
    },
    // Features
    hasLibrary: {
      type: Boolean,
      default: false,
    },
    hasComputerLab: {
      type: Boolean,
      default: false,
    },
    hasScienceLab: {
      type: Boolean,
      default: false,
    },
    hasSportsFacilities: {
      type: Boolean,
      default: false,
    },
    hasCafeteria: {
      type: Boolean,
      default: false,
    },
    hasTransportation: {
      type: Boolean,
      default: false,
    },
    hasMedicalRoom: {
      type: Boolean,
      default: false,
    },
    hasAuditorium: {
      type: Boolean,
      default: false,
    },
    hasSmartClassrooms: {
      type: Boolean,
      default: false,
    },
    additionalFacilities: {
      type: String,
      trim: true,
    },

    // Media & Documents
    schoolLogo: {
      type: String,
      trim: true,
    },
    registrationDocument: {
      type: String,
      trim: true,
    },

    // Subscription Information
    subscriptionPlan: {
      type: String,
      required: [true, "Subscription plan is required"],
      enum: ["basic", "standard", "enterprise"],
      trim: true,
    },
    subscriptionStartDate: {
      type: Date,
      required: [true, "Subscription start date is required"],
    },
    subscriptionDuration: {
      type: String,
      required: [true, "Subscription duration is required"],
      trim: true,
    },
    paymentMethod: {
      type: String,
      required: [true, "Payment method is required"],
      trim: true,
    },

    // Management Features
    hasStudentManagement: {
      type: Boolean,
      default: false,
    },
    hasStaffManagement: {
      type: Boolean,
      default: false,
    },
    hasAttendanceTracking: {
      type: Boolean,
      default: false,
    },
    hasFeeManagement: {
      type: Boolean,
      default: false,
    },
    hasExamManagement: {
      type: Boolean,
      default: false,
    },
    hasTransportManagement: {
      type: Boolean,
      default: false,
    },
    hasHostelManagement: {
      type: Boolean,
      default: false,
    },
    hasInventoryManagement: {
      type: Boolean,
      default: false,
    },
    hasAdvancedReports: {
      type: Boolean,
      default: false,
    },

    // Status
    status: {
      type: String,
      enum: ["active", "inactive", "pending", "suspended"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("School", SchoolSchema);
