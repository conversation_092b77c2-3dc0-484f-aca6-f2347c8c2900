import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreVertical,
  CheckCircle,
  XCircle,
  Mail,
  Phone,
  Copy,
  Trash2,
  Loader2,
} from "lucide-react";
import { toast } from "sonner";

export function ProfileBanner({
  parent,
  isLoading,
  className,
  setOpenStatusChange,
  setOpenDelete,
}) {
  const copyToClipboard = (text, label) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard`);
  };

  return (
    <Card
      className={`border-0 bg-gradient-to-r from-primary/10 to-secondary/10 overflow-hidden relative ${className}`}
    >
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -mr-16 -mt-16"></div>
      <CardContent>
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row sm:items-start gap-4 sm:gap-6 flex-1">
            <div className="flex justify-center sm:justify-start">
              {isLoading ? (
                <Skeleton className="h-20 w-20 sm:h-24 sm:w-24 rounded-full" />
              ) : (
                <Avatar className="h-20 w-20 sm:h-24 sm:w-24 border-4 shadow-lg">
                  <AvatarImage
                    src="/placeholder.svg"
                    alt={`${parent?.firstName} ${parent?.lastName}`}
                  />
                  <AvatarFallback className="text-xl sm:text-2xl font-bold bg-primary text-primary-foreground">
                    {parent?.firstName?.charAt(0) || ""}
                    {parent?.lastName?.charAt(0) || ""}
                  </AvatarFallback>
                </Avatar>
              )}
            </div>

            <div className="space-y-3 text-center sm:text-left flex-1">
              <div>
                {isLoading ? (
                  <Skeleton className="h-8 sm:h-9 w-3/4 mx-auto sm:mx-0 mb-2" />
                ) : (
                  <h1 className="text-2xl sm:text-3xl font-bold text-foreground mb-1 break-words">
                    {parent?.title || ""} {parent?.firstName || ""}{" "}
                    {parent?.lastName || ""}
                  </h1>
                )}
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4 text-muted-foreground">
                  <div className="flex items-center justify-center sm:justify-start space-x-1">
                    <Mail className="h-4 w-4 flex-shrink-0" />
                    {isLoading ? (
                      <Skeleton className="h-4 w-32" />
                    ) : (
                      <span className="font-medium text-sm break-all">
                        {parent?.email || "No email provided"}
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-center sm:justify-start space-x-1">
                    <Phone className="h-4 w-4 flex-shrink-0" />
                    {isLoading ? (
                      <Skeleton className="h-4 w-24" />
                    ) : (
                      <span className="text-sm">
                        {parent?.phone || "No phone provided"}
                      </span>
                    )}
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-3">
                {isLoading ? (
                  <>
                    <Skeleton className="h-6 w-20 mx-auto sm:mx-0" />
                    <Skeleton className="h-4 w-32 mx-auto sm:mx-0" />
                  </>
                ) : (
                  <>
                    <Badge
                      variant={
                        parent?.status === "active" ? "default" : "secondary"
                      }
                      className={`px-3 py-1 w-fit mx-auto sm:mx-0 ${
                        parent?.status === "active"
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground"
                      }`}
                    >
                      {parent?.status === "active" ? (
                        <CheckCircle className="h-3 w-3 mr-1" />
                      ) : (
                        <XCircle className="h-3 w-3 mr-1" />
                      )}
                      {parent?.status
                        ? parent.status.charAt(0).toUpperCase() +
                          parent.status.slice(1)
                        : "Unknown"}
                    </Badge>
                    <span className="text-xs text-muted-foreground font-mono text-center sm:text-left break-all">
                      ID: {parent?._id || "Loading..."}
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-center sm:justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={isLoading}>
                  {isLoading ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <MoreVertical className="h-4 w-4" />
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-48">
                <DropdownMenuItem
                  onClick={() => copyToClipboard(parent?._id, "Parent ID")}
                  disabled={!parent?._id || isLoading}
                >
                  <Copy className="h-4 w-4 mr-2" />
                  Copy ID
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setOpenStatusChange(true)}
                  disabled={isLoading}
                >
                  {parent?.status === "active" ? (
                    <>
                      <XCircle className="h-4 w-4 mr-2" />
                      Deactivate
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Activate
                    </>
                  )}
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => setOpenDelete(true)}
                  className="text-destructive/90 focus:text-destructive"
                  disabled={isLoading}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Parent
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
