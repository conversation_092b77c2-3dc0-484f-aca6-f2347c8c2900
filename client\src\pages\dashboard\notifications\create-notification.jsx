import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { NotificationForm } from "@/components/forms/dashboard/notifications/notification-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useNotification } from "@/context/notification-context";

const CreateNotification = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(!!id);
  const [notificationData, setNotificationData] = useState(null);
  const { fetchNotificationById } = useNotification();

  useEffect(() => {
    const loadAdminData = async () => {
      if (id) {
        try {
          const data = await fetchNotificationById(id);
          setNotificationData(data);
        } catch (error) {
          console.error("Failed to fetch notification data:", error);
          toast.error("Failed to load notification data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };
    loadAdminData();
  }, [id, fetchNotificationById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Notification" : "Create New Notification"}
        actions={[
          { label: "Back to Notifications", href: "/dashboard/notifications" },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Notifications", href: "/dashboard/notifications" },
          { label: id ? "Edit Notification" : "Create Notification" },
        ]}
      />
      {loading ? (
        <FormCardSkeleton />
      ) : (
        <NotificationForm editingId={id} initialData={notificationData} />
      )}
    </Container>
  );
};

export default CreateNotification;
