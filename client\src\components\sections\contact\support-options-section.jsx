import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  PhoneIcon,
  MailIcon,
  BookOpenIcon,
  ClockIcon,
  ArrowRightIcon,
  HeadphonesIcon,
} from "lucide-react";
import { Container } from "@/components/ui/container";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";

export function SupportOptionsSection({
  title = "Multiple Ways to Get Support",
  description = "Choose the support option that works best for you. Our dedicated team is here to ensure your success with School TRACK.",
  badge = (
    <Announcement>
      <AnnouncementTag>Support</AnnouncementTag>
      <AnnouncementTitle>We're Here to Help</AnnouncementTitle>
    </Announcement>
  ),
  supportOptions = [
    {
      icon: <PhoneIcon className="size-6" />,
      title: "Phone Support",
      description: "Speak directly with our support team for assistance.",
      details: "Available Mon-Fri: 8:00 AM - 6:00 PM EST",
      action: "Call Now",
      actionHref: "tel:+***********",
      actionVariant: "default",
      badge: "Live Help",
      priority: "high",
    },
    {
      icon: <MailIcon className="size-6" />,
      title: "Email Support",
      description: "Send detailed questions and view responses.",
      details: "Response within 4-6 hours during business days",
      action: "Send Email",
      actionHref: "mailto:<EMAIL>",
      actionVariant: "outline",
      badge: "Detailed",
      priority: "medium",
    },
    {
      icon: <BookOpenIcon className="size-6" />,
      title: "Knowledge Base",
      description: "Browse our comprehensive library of guides.",
      details: "Over 200+ articles and step-by-step guides",
      action: "Browse Docs",
      actionHref: "/knowledge-base",
      actionVariant: "ghost",
      badge: "Self-Service",
      priority: "low",
    },
  ],
  emergencySupport = {
    show: true,
    title: "Emergency Support",
    description:
      "Critical system issues? Our emergency line is available 24/7 for urgent matters.",
    phone: "+1 (555) 911-HELP",
    email: "<EMAIL>",
  },
  className,
}) {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "ring-2 ring-primary/20 bg-primary/5";
      case "medium":
        return "ring-1 ring-border";
      case "low":
        return "";
      default:
        return "";
    }
  };

  const getBadgeVariant = (priority) => {
    switch (priority) {
      case "high":
        return "default";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <div className={cn("overflow-hidden py-16 sm:py-24", className)}>
      <Container className="gap-12">
        <div className="flex flex-col items-center gap-6 text-center sm:gap-8">
          {badge}

          <h2 className="animate-appear from-foreground to-foreground dark:to-muted-foreground relative z-10 inline-block bg-linear-to-r bg-clip-text text-3xl leading-tight font-semibold text-balance text-transparent drop-shadow-2xl opacity-0 delay-100 sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
            {title}
          </h2>

          <p className="text-md animate-appear text-muted-foreground relative z-10 max-w-[640px] font-medium text-balance opacity-0 delay-200 sm:text-lg">
            {description}
          </p>
        </div>
        <div className="animate-appear mt-8 relative z-10 grid gap-6 opacity-0 delay-300 sm:grid-cols-2 lg:grid-cols-3">
          {supportOptions.map((option, index) => (
            <div
              key={index}
              className={cn(
                "group relative overflow-hidden rounded-2xl border p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg",
                "bg-card/50 hover:bg-card/80",
                getPriorityColor(option.priority)
              )}
            >
              <div className="mb-4 flex items-center justify-between">
                <Badge
                  variant={getBadgeVariant(option.priority)}
                  className="text-xs"
                >
                  {option.badge}
                </Badge>
                {option.priority === "high" && (
                  <div className="h-2 w-2 rounded-full bg-primary animate-pulse" />
                )}
              </div>
              <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                {option.icon}
              </div>
              <h3 className="mb-2 text-lg font-semibold text-card-foreground">
                {option.title}
              </h3>

              <p className="mb-3 text-sm text-muted-foreground">
                {option.description}
              </p>

              <div className="mb-4 flex items-center gap-2 text-xs text-muted-foreground">
                <ClockIcon className="size-3" />
                {option.details}
              </div>
              <Button
                variant={option.actionVariant}
                size="sm"
                className="w-full"
                asChild
              >
                <a href={option.actionHref}>
                  {option.action}
                  <ArrowRightIcon className="ml-2 size-3" />
                </a>
              </Button>
            </div>
          ))}
        </div>

        {emergencySupport.show && (
          <div className="animate-appear relative z-10 opacity-0 delay-500 mt-8">
            <div className="group relative overflow-hidden rounded-2xl border border-destructive/20 bg-destructive/5 p-6 backdrop-blur-sm transition-all duration-300 hover:bg-destructive/10 hover:shadow-lg">
              <div className="flex items-start gap-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-destructive/10 text-destructive">
                  <HeadphonesIcon className="size-6" />
                </div>

                <div className="flex-1">
                  <h3 className="mb-2 text-lg font-semibold text-card-foreground">
                    {emergencySupport.title}
                  </h3>
                  <p className="mb-4 text-sm text-muted-foreground">
                    {emergencySupport.description}
                  </p>

                  <div className="flex flex-wrap gap-3">
                    <Button variant="destructive" size="sm" asChild>
                      <a href={`tel:${emergencySupport.phone}`}>
                        <PhoneIcon className="mr-2 size-3" />
                        {emergencySupport.phone}
                      </a>
                    </Button>
                    <Button variant="outline" size="sm" asChild>
                      <a href={`mailto:${emergencySupport.email}`}>
                        <MailIcon className="mr-2 size-3" />
                        Emergency Email
                      </a>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </Container>
    </div>
  );
}
