import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { CalendarDays, Clock } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export function WelcomeBanner({
  user,
  title,
  isLoading,
  subtitle,
  showDate = true,
  showTime = true,
  className = "",
  actionButton = null,
}) {
  const formattedDate = new Date().toLocaleDateString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  const formattedTime = new Date().toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });

  return (
    <Card
      className={`border-0 bg-gradient-to-r from-primary/10 to-secondary/10 overflow-hidden relative ${className}`}
    >
      {/* Decorative elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-primary/5 rounded-full -mr-16 -mt-16"></div>

      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-6 relative z-10">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              {isLoading ? (
                <Skeleton className="h-8 md:h-10 w-64 md:w-80" />
              ) : (
                <h1 className="text-2xl md:text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/70">
                  {`Welcome back, ${user?.name || "User"}` || title}
                </h1>
              )}
            </div>
            <p className="text-muted-foreground">
              {subtitle || "Welcome to your dashboard"}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
            {showDate && (
              <div className="flex items-center gap-2 text-sm bg-background/80 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm">
                <CalendarDays className="h-4 w-4 text-primary" />
                <span>{formattedDate}</span>
              </div>
            )}

            {showTime && (
              <div className="flex items-center gap-2 text-sm bg-background/80 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm">
                <Clock className="h-4 w-4 text-primary" />
                <span>{formattedTime}</span>
              </div>
            )}

            {actionButton && <div className="ml-2">{actionButton}</div>}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
