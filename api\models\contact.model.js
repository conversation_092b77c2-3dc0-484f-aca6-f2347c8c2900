import mongoose from "mongoose";

const ContactSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, "Name is required"],
      trim: true,
    },
    email: {
      type: String,
      required: [true, "Email is required"],
      trim: true,
    },
    phone: {
      type: String,
      trim: true,
    },
    schoolName: {
      type: String,
      trim: true,
    },
    schoolEmail: {
      type: String,
      trim: true,
    },
    schoolWebsite: {
      type: String,
      trim: true,
    },
    role: {
      type: String,
      trim: true,
    },
    schoolSize: {
      type: String,
      trim: true,
    },
    inquiryType: {
      type: String,
      required: [true, "Inquiry type is required"],
      trim: true,
    },
    message: {
      type: String,
      required: [true, "Message is required"],
      trim: true,
    },
    newsletter: {
      type: Boolean,
      default: false,
    },
    status: {
      type: String,
      enum: ["new", "contacted", "resolved", "archived"],
      default: "new",
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("Contact", ContactSchema);
