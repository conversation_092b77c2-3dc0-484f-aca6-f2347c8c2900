import React, { useEffect, useState } from "react";
import {
  getCoreRowModel,
  getFacetedRowModel,
  getFacetedUniqueValues,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { TableContent } from "@/components/data-table/data-table-component/table-content";
import { SearchBar } from "@/components/data-table/data-table-component/search-bar";
import { DateFilter } from "@/components/data-table/data-table-component/date-filter";
import { DataTableViewOptions } from "@/components/data-table/data-table-component/data-table-view-options";
import { DateRangeFilter } from "@/components/data-table/data-table-component/date-range-filter";
import { DataTablePagination } from "@/components/data-table/data-table-component/data-table-pagination";

export function DataTable({ data: initialData, columns }) {
  const [data, setData] = useState(() => initialData || []);
  const [rowSelection, setRowSelection] = useState({});
  const [columnVisibility, setColumnVisibility] = useState({});
  const [columnFilters, setColumnFilters] = useState([]);
  const [searchResults, setSearchResults] = useState(data);
  const [filteredData, setFilteredData] = useState(data);
  const [isSearch, setIsSearch] = useState(true);
  const [sorting, setSorting] = useState([]);

  useEffect(() => {
    if (initialData) {
      setData(initialData);
    }
  }, [initialData]);

  const table = useReactTable({
    data: isSearch ? searchResults : filteredData,
    columns,
    state: {
      sorting,
      columnVisibility,
      rowSelection,
      columnFilters,
    },
    enableRowSelection: true,
    onRowSelectionChange: setRowSelection,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    onColumnVisibilityChange: setColumnVisibility,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFacetedRowModel: getFacetedRowModel(),
    getFacetedUniqueValues: getFacetedUniqueValues(),
  });

  return (
    <div className="w-full flex-col justify-start gap-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-2">
        <div className="flex flex-col sm:flex-row gap-2 flex-1 ">
          <SearchBar
            data={data}
            onSearch={setSearchResults}
            setIsSearch={setIsSearch}
          />
          <DateRangeFilter
            data={data}
            setIsSearch={setIsSearch}
            onFilter={setFilteredData}
          />
        </div>
        <div className="flex items-center gap-2">
          <DateFilter
            data={data}
            setIsSearch={setIsSearch}
            onFilter={setFilteredData}
          />
          <DataTableViewOptions table={table} />
        </div>
      </div>
      <div className="relative flex flex-col gap-4 overflow-auto mt-4">
        <TableContent table={table} columns={columns} />
        <DataTablePagination table={table} />
      </div>
    </div>
  );
}
