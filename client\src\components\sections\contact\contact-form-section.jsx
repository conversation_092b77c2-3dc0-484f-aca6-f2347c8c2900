import { cn } from "@/lib/utils";
import { Container } from "@/components/ui/container";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import ContactForm from "@/components/forms/contact/contact-form";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  MessageSquare,
  CalendarClock,
  ArrowRightIcon,
  ClockIcon,
} from "lucide-react";

export function ContactFormSection({
  title = "We'd Love to Hear From You",
  description = "Have questions about School TRACK? Our team is here to help you find the right solution for your educational institution.",
  badge = (
    <Announcement>
      <AnnouncementTag>Contact Us</AnnouncementTag>
      <AnnouncementTitle>Get in Touch</AnnouncementTitle>
    </Announcement>
  ),
  contactOptions = [
    {
      icon: <MessageSquare className="size-6" />,
      title: "Speak to Sales",
      description:
        "Schedule a call with our sales team for personalized guidance.",
      details: "Available Mon-Fri: 9:00 AM - 5:00 PM",
      action: "Book Appointment",
      actionHref: "/contact-us",
      actionVariant: "default",
      badge: "Recommended",
      priority: "high",
    },
    {
      icon: <CalendarClock className="size-6" />,
      title: "Schedule Demo",
      description: "See School TRACK in action with a personalized demo.",
      details: "30-minute sessions available daily",
      action: "Schedule Now",
      actionHref: "/contact-us",
      actionVariant: "ghost",
      badge: "Live Demo",
      priority: "low",
    },
  ],
  className,
}) {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case "high":
        return "ring-2 ring-primary/20 bg-primary/5";
      case "medium":
        return "ring-1 ring-border";
      case "low":
        return "";
      default:
        return "";
    }
  };

  const getBadgeVariant = (priority) => {
    switch (priority) {
      case "high":
        return "default";
      case "medium":
        return "secondary";
      case "low":
        return "outline";
      default:
        return "secondary";
    }
  };

  return (
    <div className={cn("overflow-hidden py-16 sm:py-24", className)}>
      <Container className="gap-12">
        <div className="flex flex-col items-center gap-6 text-center sm:gap-8">
          {badge}

          <h2 className="animate-appear from-foreground to-foreground dark:to-muted-foreground relative z-10 inline-block bg-linear-to-r bg-clip-text text-3xl leading-tight font-semibold text-balance text-transparent drop-shadow-2xl opacity-0 delay-100 sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
            {title}
          </h2>

          <p className="text-md animate-appear text-muted-foreground relative z-10 max-w-[640px] font-medium text-balance opacity-0 delay-200 sm:text-lg">
            {description}
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4">
          <div className="animate-appear mt-8 relative z-10 grid gap-6 opacity-0 delay-300 col-span-1 md:col-span-1">
            {contactOptions.map((option, index) => (
              <div
                key={index}
                className={cn(
                  "group relative overflow-hidden rounded-2xl border p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg",
                  "bg-card/50 hover:bg-card/80",
                  getPriorityColor(option.priority)
                )}
              >
                <div className="mb-4 flex items-center justify-between">
                  <Badge
                    variant={getBadgeVariant(option.priority)}
                    className="text-xs"
                  >
                    {option.badge}
                  </Badge>
                  {option.priority === "high" && (
                    <div className="h-2 w-2 rounded-full bg-primary animate-pulse" />
                  )}
                </div>
                <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary">
                  {option.icon}
                </div>
                <h3 className="mb-2 text-lg font-semibold text-card-foreground">
                  {option.title}
                </h3>
                <p className="mb-3 text-sm text-muted-foreground">
                  {option.description}
                </p>
                <div className="mb-4 flex items-center gap-2 text-xs text-muted-foreground">
                  <ClockIcon className="size-3" />
                  {option.details}
                </div>
                <Button
                  variant={option.actionVariant}
                  size="sm"
                  className="w-full"
                  asChild
                >
                  <a href={option.actionHref}>
                    {option.action}
                    <ArrowRightIcon className="ml-2 size-3" />
                  </a>
                </Button>
              </div>
            ))}
          </div>
          <div className="animate-appear relative z-10 opacity-0 delay-400 mt-8 col-span-1 md:col-span-3">
            <div className="group relative overflow-hidden rounded-2xl border p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-lg">
              <div className="flex flex-col gap-6">
                <div>
                  <h3 className="mb-2 text-xl font-semibold text-card-foreground">
                    Send us a message
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Our team will reach out within 24 hours to schedule a
                    personalized demo and discuss your specific needs.
                  </p>
                </div>

                <ContactForm />
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
