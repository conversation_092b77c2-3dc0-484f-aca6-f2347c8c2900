import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { MapPinIcon, PhoneIcon, MailIcon, ArrowRightIcon } from "lucide-react";
import { Container } from "@/components/ui/container";
import Glow from "@/components/ui/glow";
import Screenshot from "@/components/ui/screenshot";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";

export function OfficeLocationsSection({
  title = "Our Office Locations",
  badge = (
    <Announcement>
      <AnnouncementTag>Office Locations</AnnouncementTag>
      <AnnouncementTitle>Visit Us or Get In Touch</AnnouncementTitle>
    </Announcement>
  ),
  description = "Visit us at our convenient location or get in touch with our team for personalized support and demonstrations.",
  locations = [
    {
      name: "Headquarters",
      address: "123 Education Boulevard\nSuite 200\nNew York, NY 10001",
      phone: "+****************",
      email: "<EMAIL>",
      hours: "Mon-Fri: 8:00 AM - 6:00 PM\nSat: 9:00 AM - 2:00 PM",
      badge: "Main Office",
      isPrimary: true,
    },
  ],
  className,
}) {
  return (
    <div className={cn("overflow-hidden py-16 sm:py-24", className)}>
      <Container className="gap-12">
        <div className="flex flex-col items-center gap-6 text-center sm:gap-8">
          {badge}
          <h2 className="animate-appear from-foreground to-foreground dark:to-muted-foreground relative z-10 inline-block bg-linear-to-r bg-clip-text text-3xl leading-tight font-semibold text-balance text-transparent drop-shadow-2xl sm:text-4xl sm:leading-tight md:text-5xl md:leading-tight">
            {title}
          </h2>
          <p className="text-md animate-appear text-muted-foreground relative z-10 max-w-[640px] font-medium text-balance opacity-0 delay-100 sm:text-lg">
            {description}
          </p>
        </div>

        <div className="animate-appear relative z-10 grid gap-6 opacity-0 delay-200 grid-cols-1 lg:grid-cols-2 items-start mt-8">
          <div className="space-y-6">
            {locations.map((location, index) => (
              <div
                key={index}
                className={cn(
                  "group relative overflow-hidden rounded-2xl border bg-card/50 p-4 sm:p-6 backdrop-blur-sm transition-all duration-300 hover:bg-card/80 hover:shadow-lg",
                  location.isPrimary && "ring-2 ring-primary/20"
                )}
              >
                <div className="mb-4 flex items-center justify-between">
                  <Badge
                    variant={location.isPrimary ? "default" : "secondary"}
                    className="text-xs"
                  >
                    {location.badge}
                  </Badge>
                  {location.isPrimary && (
                    <div className="absolute -right-2 -top-2 h-4 w-4 rounded-full bg-primary/20" />
                  )}
                </div>
                <h3 className="mb-4 text-lg sm:text-xl font-semibold text-card-foreground">
                  {location.name}
                </h3>
                <div className="mb-4 flex items-start gap-3">
                  <MapPinIcon className="mt-1 size-4 text-muted-foreground flex-shrink-0" />
                  <div className="text-sm text-muted-foreground">
                    {location.address.split("\n").map((line, i) => (
                      <div key={i}>{line}</div>
                    ))}
                  </div>
                </div>
                <div className="mb-4 flex items-center gap-3">
                  <PhoneIcon className="size-4 text-muted-foreground flex-shrink-0" />
                  <a
                    href={`tel:${location.phone}`}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {location.phone}
                  </a>
                </div>
                <div className="mb-4 flex items-center gap-3">
                  <MailIcon className="size-4 text-muted-foreground flex-shrink-0" />
                  <a
                    href={`mailto:${location.email}`}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {location.email}
                  </a>
                </div>
                <div className="border-t pt-4">
                  <h4 className="mb-2 text-sm font-medium text-card-foreground">
                    Office Hours
                  </h4>
                  <div className="text-xs text-muted-foreground">
                    {location.hours.split("\n").map((line, i) => (
                      <div key={i}>{line}</div>
                    ))}
                  </div>
                </div>
                <div className="absolute inset-0 -z-10 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                  <Glow variant="bottom" className="h-full w-full" />
                </div>
              </div>
            ))}
          </div>

          {/* Map Section */}
          <div className="relative mt-2 sm:mt-0">
            <div className="group relative overflow-hidden rounded-2xl border bg-card/50 backdrop-blur-sm transition-all duration-300 hover:bg-card/80 hover:shadow-lg">
              <div className="w-full h-auto aspect-video sm:aspect-auto">
                <Screenshot
                  srcLight="/map-light.png"
                  srcDark="/map-dark.png"
                  alt="Office location map"
                  height={355}
                  className="object-cover object-center"
                />
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-background/20 to-transparent" />
              <div className="absolute bottom-4 left-4 right-4">
                <div className="rounded-lg bg-background/90 p-3 sm:p-4 backdrop-blur-sm">
                  <p className="text-sm font-medium text-foreground">
                    123 Education Boulevard, Suite 200
                  </p>
                  <p className="text-xs text-muted-foreground">
                    New York, NY 10001
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2 w-full sm:w-auto"
                    asChild
                  >
                    <a
                      href="https://maps.google.com/?q=123+Education+Boulevard+Suite+200+New+York+NY+10001"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      Get Directions
                      <ArrowRightIcon className="ml-1 size-3" />
                    </a>
                  </Button>
                </div>
              </div>
              <div className="absolute inset-0 -z-10 opacity-0 transition-opacity duration-300 group-hover:opacity-100">
                <Glow variant="bottom" className="h-full w-full" />
              </div>
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
}
