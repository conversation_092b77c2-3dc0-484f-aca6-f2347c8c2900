import React, { useState, useId } from "react";
import {
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormControl,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Upload, X } from "lucide-react";
import { cn } from "@/lib/utils";

export function FileInput({
  form,
  name,
  label,
  description,
  accept,
  disabled,
  className,
  icon: Icon,
}) {
  const [preview, setPreview] = useState(null);
  const [fileName, setFileName] = useState(null);
  const inputId = useId();

  const handleFileChange = (event, onChange) => {
    const file = event.target.files?.[0];
    if (file) {
      setFileName(file.name);
      onChange(file);

      if (file.type.startsWith("image/")) {
        const reader = new FileReader();
        reader.onloadend = () => {
          setPreview(reader.result);
        };
        reader.readAsDataURL(file);
      } else {
        setPreview(null);
      }
    }
  };

  const clearFile = (onChange) => {
    setFileName(null);
    setPreview(null);
    onChange(null);
  };

  return (
    <FormField
      control={form.control}
      name={name}
      render={({ field: { onChange, value, ...field } }) => (
        <FormItem className={cn("space-y-2", className)}>
          <FormLabel className="form-label" htmlFor={inputId}>
            {Icon && <Icon className="h-4 w-4 mr-1" />}
            {label}
          </FormLabel>
          <FormControl>
            <div className="space-y-4">
              {!fileName ? (
                <div
                  className={cn(
                    "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
                    "border-border hover:border-primary/50",
                    "bg-background hover:bg-accent/50",
                    disabled && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <Upload className="mx-auto h-12 w-12 text-muted-foreground" />
                  <p className="mt-2 text-sm text-muted-foreground">
                    {description || "Click to upload file"}
                  </p>
                  <input
                    id={inputId}
                    type="file"
                    className="hidden"
                    accept={accept}
                    disabled={disabled}
                    onChange={(e) => handleFileChange(e, onChange)}
                    {...field}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="mt-2"
                    disabled={disabled}
                    onClick={() => {
                      document.getElementById(inputId)?.click();
                    }}
                  >
                    Choose File
                  </Button>
                </div>
              ) : (
                <div className="border border-border rounded-lg p-4 bg-card">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      {preview ? (
                        <img
                          src={preview || "/placeholder.svg"}
                          alt="Preview"
                          className="h-12 w-12 object-cover rounded border border-border"
                        />
                      ) : (
                        <div className="h-12 w-12 bg-muted rounded flex items-center justify-center border border-border">
                          <Upload className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                      <div>
                        <p className="text-sm font-medium text-card-foreground">
                          {fileName}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          File selected
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFile(onChange)}
                      disabled={disabled}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </FormControl>
          <FormMessage className="form-error" />
        </FormItem>
      )}
    />
  );
}
