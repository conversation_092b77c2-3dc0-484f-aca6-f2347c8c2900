import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { AtSignIcon, LockIcon, UserIcon, UserPlus } from "lucide-react";
import { SubmitButton } from "@/components/form-inputs/submit-button";
import { signUpSchema } from "@/schema/auth";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/auth-context";

export function SignUpForm() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { register } = useAuth();

  const form = useForm({
    resolver: zodResolver(signUpSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
    },
  });

  const handleSubmit = async (data) => {
    setIsLoading(true);

    try {
      const result = await register(data);
      toast.success("Account Created Successfully");

      // Redirect to dashboard directly after successful registration
      navigate("/dashboard");
    } catch (error) {
      console.error("Sign up error:", error);
      toast.error(error.message || "Failed to create account");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8 w-full max-w-md">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <TextInput
              form={form}
              name="firstName"
              label="First Name"
              placeholder="Enter your first name"
              icon={UserIcon}
              validation={{
                required: "First name is required",
              }}
            />

            <TextInput
              form={form}
              name="lastName"
              label="Last Name"
              placeholder="Enter your last name"
              icon={UserIcon}
              validation={{
                required: "Last name is required",
              }}
            />
          </div>

          <TextInput
            form={form}
            name="email"
            label="Email"
            placeholder="Enter your email address"
            icon={AtSignIcon}
            validation={{
              required: "Email is required",
            }}
          />

          <PasswordInput
            form={form}
            name="password"
            label="Password"
            placeholder="Create a strong password"
            icon={LockIcon}
            validation={{
              required: "Password is required",
            }}
          />

          <SubmitButton
            buttonIcon={UserPlus}
            title="Create Account"
            loading={isLoading}
            loadingTitle="Creating account..."
            className="w-full mt-6"
          />

          <div className="text-center">
            <span className="text-sm text-muted-foreground">
              Already have an account?{" "}
            </span>
            <a href="/sign-in" className="text-sm text-primary hover:underline">
              Sign in
            </a>
          </div>
        </form>
      </Form>
    </div>
  );
}
