import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  filterByLast7Days,
  filterByThisMonth,
  filterByThisYear,
  filterByToday,
  filterByYesterday,
} from "@/utils/date-filters";
import { SlidersHorizontal } from "lucide-react";

const DEFAULT_OPTIONS = [
  { value: "life", label: "Life time" },
  { value: "today", label: "Today" },
  { value: "yesterday", label: "Yesterday" },
  { value: "last-7-days", label: "Last 7 days" },
  { value: "month", label: "This Month" },
  { value: "year", label: "This Year" },
];

export function DateFilter({
  data,
  onFilter,
  setIsSearch,
  options = DEFAULT_OPTIONS,
  className = "",
  triggerClassName = "w-full",
  placeholder = "Select time period",
}) {
  const [selectedFilter, setSelectedFilter] = useState(options[0].value);

  const handleChange = (valueString) => {
    options.find((option) => option.value === valueString);
    setSelectedFilter(valueString);
    setIsSearch(false);

    let filteredData;

    switch (valueString) {
      case "life":
        filteredData = data;
        break;
      case "today":
        filteredData = filterByToday(data);
        break;
      case "yesterday":
        filteredData = filterByYesterday(data);
        break;
      case "last-7-days":
        filteredData = filterByLast7Days(data);
        break;
      case "month":
        filteredData = filterByThisMonth(data);
        break;
      case "year":
        filteredData = filterByThisYear(data);
        break;
      default:
        filteredData = data;
    }

    onFilter(filteredData);
  };

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Select defaultValue={selectedFilter} onValueChange={handleChange}>
        <SelectTrigger className={triggerClassName}>
          <SlidersHorizontal className="h-4 w-4" />
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
