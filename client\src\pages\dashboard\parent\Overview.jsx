import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Users, ClipboardCheck, Calendar, Award } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";

const ParentOverview = () => {
  const { user, isLoading } = useAuth();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="Parent Dashboard"
        subtitle="Monitor your children's academic progress and school activities"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="My Children"
          value="2"
          description="Enrolled in the school"
          icon={Users}
          isLoading={isLoading}
        />

        <StatCard
          title="Pending Assignments"
          value="7"
          description="Due this week"
          icon={ClipboardCheck}
          isLoading={isLoading}
          trend="negative"
        />

        <StatCard
          title="Upcoming Events"
          value="3"
          description="Parent-teacher meetings"
          icon={Calendar}
          isLoading={isLoading}
        />

        <StatCard
          title="Average GPA"
          value="3.6"
          description="Across all children"
          icon={Award}
          isLoading={isLoading}
          trend="positive"
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Academic Performance</CardTitle>
            <CardDescription>
              Your children's grades across all subjects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] bg-muted/20 rounded-md flex items-center justify-center">
              Performance Chart
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Attendance Overview</CardTitle>
            <CardDescription>
              Attendance records for the current semester
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] bg-muted/20 rounded-md flex items-center justify-center">
              Attendance Chart
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Recent Communications</CardTitle>
          <CardDescription>
            Messages from teachers and school administration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="text-xs font-medium">JD</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">John Doe (Math Teacher)</p>
                  <p className="text-xs text-muted-foreground">2 days ago</p>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Your child has been performing exceptionally well in the
                  recent mathematics tests. I'd like to discuss potential
                  advanced placement options.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                <span className="text-xs font-medium">SA</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">School Administration</p>
                  <p className="text-xs text-muted-foreground">1 week ago</p>
                </div>
                <p className="text-sm text-muted-foreground mt-1">
                  Reminder: The parent-teacher conference is scheduled for next
                  Friday. Please confirm your attendance.
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ParentOverview;
