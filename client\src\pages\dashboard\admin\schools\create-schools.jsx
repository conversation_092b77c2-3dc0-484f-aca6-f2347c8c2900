import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { SchoolForm } from "@/components/forms/dashboard/schools/school-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { useSchool } from "@/context/school-context";
import { toast } from "sonner";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";

const CreateSchools = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchSchoolById, isLoading } = useSchool();
  const [schoolData, setSchoolData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadSchoolData = async () => {
      if (id) {
        try {
          const data = await fetchSchoolById(id);
          setSchoolData(data);
        } catch (error) {
          console.error("Failed to fetch school data:", error);
          toast.error("Failed to load school data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadSchoolData();
  }, [id, fetchSchoolById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit School" : "Create New School"}
        actions={[
          {
            label: "Back to Schools",
            href: "/dashboard/schools",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Schools", href: "/dashboard/schools" },
          { label: id ? "Edit School" : "Create School" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <SchoolForm editingId={id} initialData={schoolData?.data} />
      )}
    </Container>
  );
};

export default CreateSchools;
