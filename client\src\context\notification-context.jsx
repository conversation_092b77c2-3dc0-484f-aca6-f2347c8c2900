import { createContext, useContext, useState } from "react";
import {
  createNotification,
  getNotifications,
  getNotificationById,
  updateNotification,
  deleteNotification,
  sendNotification,
  markNotificationAsRead,
} from "@/api/notification-api";

const NotificationContext = createContext({
  notifications: [],
  currentNotification: null,
  isLoading: false,
  error: null,
  addNotification: () => {},
  fetchAllNotifications: () => {},
  fetchNotificationById: () => {},
  editNotification: () => {},
  removeNotification: () => {},
  sendNotificationNow: () => {},
  markAsRead: () => {},
});

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const [currentNotification, setCurrentNotification] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addNotification = async (notificationData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createNotification(notificationData);
      setNotifications((prevNotifications) => [
        response.data,
        ...prevNotifications,
      ]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create notification");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllNotifications = async (filters = {}) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getNotifications(filters);
      setNotifications(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch notifications");
      setNotifications([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchNotificationById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getNotificationById(id);
      const notificationData = response.data;
      setCurrentNotification(notificationData);
      setIsLoading(false);
      return notificationData;
    } catch (error) {
      setError(error.message || `Failed to fetch notification with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editNotification = async (id, notificationData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateNotification(id, notificationData);
      setNotifications(
        notifications.map((notification) =>
          notification._id === id
            ? { ...notification, ...response.data }
            : notification
        )
      );
      if (currentNotification && currentNotification._id === id) {
        setCurrentNotification({ ...currentNotification, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update notification with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeNotification = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteNotification(id);
      setNotifications(
        notifications.filter((notification) => notification._id !== id)
      );
      if (currentNotification && currentNotification._id === id) {
        setCurrentNotification(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete notification with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const sendNotificationNow = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await sendNotification(id);
      setNotifications(
        notifications.map((notification) =>
          notification._id === id
            ? { ...notification, status: "sent", sentAt: new Date() }
            : notification
        )
      );
      if (currentNotification && currentNotification._id === id) {
        setCurrentNotification({
          ...currentNotification,
          status: "sent",
          sentAt: new Date(),
        });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to send notification with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const markAsRead = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await markNotificationAsRead(id);
      // Update the notification in the list
      setNotifications(
        notifications.map((notification) =>
          notification._id === id
            ? { ...notification, isRead: true }
            : notification
        )
      );
      if (currentNotification && currentNotification._id === id) {
        setCurrentNotification({ ...currentNotification, isRead: true });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(
        error.message || `Failed to mark notification with ID: ${id} as read`
      );
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        currentNotification,
        isLoading,
        error,
        addNotification,
        fetchAllNotifications,
        fetchNotificationById,
        editNotification,
        removeNotification,
        sendNotificationNow,
        markAsRead,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotification = () => useContext(NotificationContext);
