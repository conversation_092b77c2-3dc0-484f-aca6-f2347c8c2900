import * as React from "react";
import { <PERSON> } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Menu, User } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  navigationMenuTriggerStyle,
} from "@/components/ui/navigation-menu";
import {
  Sheet,
  <PERSON>et<PERSON>ontent,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  SheetDescription,
} from "@/components/ui/sheet";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Container } from "@/components/ui/container";
import Logo from "@/components/logos/logo";
import { useAuth } from "@/context/auth-context";

export default function SchoolNavbar({
  // Logo props
  logo = <Logo />,
  logoTitle = "School Track",
  logoDescription = "Modern school management dashboard built with React, Shadcn/ui and Tailwind CSS.",
  logoHref = "/",

  // Main navigation structure
  menuItems = [
    {
      title: "Overview",
      content: "overview",
    },
    {
      title: "Teachers",
      content: "teachers",
    },
    {
      title: "Students",
      content: "students",
    },
    {
      title: "Parents",
      content: "parents",
    },
    {
      title: "Admin",
      isLink: true,
      href: "/admin",
    },
  ],

  // Mobile navigation
  mobileLinks = [
    { text: "Overview", href: "/overview" },
    { text: "Teachers", href: "/dashboard/teachers" },
    { text: "Students", href: "/dashboard/students" },
    { text: "Parents", href: "/dashboard/parents" },
    { text: "Admin", href: "/admin" },
  ],

  // Overview menu items
  overviewItems = [
    {
      title: "Overview",
      href: "/dashboard",
      description: "Complete system overview and statistics.",
    },
    {
      title: "Calendar",
      href: "/dashboard/calendar",
      description: "School-wide calendar for events and activities.",
    },
    {
      title: "Announcements",
      href: "/dashboard/announcements",
      description: "Publish and manage school-wide announcements.",
    },
  ],

  // Teacher section items
  teacherItems = [
    {
      title: "Attendance",
      href: "/dashboard/teachers/attendance",
      description: "Track and manage daily attendance for teaching staff.",
    },
    {
      title: "Assignments",
      href: "/dashboard/teachers/assignments",
      description: "Create and review assignments for different classes.",
    },
    {
      title: "Reports",
      href: "/dashboard/teachers/reports",
      description: "Generate performance and attendance reports for classes.",
    },
    {
      title: "Time Table",
      href: "/dashboard/teachers/timetable",
      description: "View and manage teaching schedules and allocations.",
    },
    {
      title: "Notifications",
      href: "/dashboard/teachers/notifications",
      description: "Send important updates to students and parents.",
    },
    {
      title: "Exams",
      href: "/dashboard/teachers/exams",
      description: "Schedule exams and publish results efficiently.",
    },
  ],

  // Student section items
  studentItems = [
    {
      title: "Enrollment",
      href: "/dashboard/students/enrollment",
      description: "Manage student admissions and registration process.",
    },
    {
      title: "Attendance",
      href: "/dashboard/students/attendance",
      description: "Track and manage daily attendance for all students.",
    },
    {
      title: "Performance",
      href: "/dashboard/students/performance",
      description: "Monitor academic progress and generate report cards.",
    },
    {
      title: "Fees",
      href: "/dashboard/students/fees",
      description: "Manage fee collection and payment tracking.",
    },
    {
      title: "Behavior",
      href: "/dashboard/students/behavior",
      description: "Track student behavior, rewards, and disciplinary actions.",
    },
    {
      title: "Health Records",
      href: "/dashboard/students/health",
      description: "Maintain student health information and medical records.",
    },
  ],

  // Parent section items
  parentItems = [
    {
      title: "Communication",
      href: "/dashboard/parents/communication",
      description: "Communicate with teachers and administrators.",
    },
    {
      title: "Student Progress",
      href: "/dashboard/parents/progress",
      description: "Track academic performance and attendance.",
    },
    {
      title: "Payments",
      href: "/dashboard/parents/payments",
      description: "View and manage school fee payments.",
    },
    {
      title: "Events",
      href: "/dashboard/parents/events",
      description: "View upcoming school events and activities.",
    },
  ],

  // Authentication actions
  actions = [
    {
      text: "Sign In",
      href: "/sign-in",
      isButton: true,
      variant: "outline",
    },
    {
      text: "Contact us",
      href: "/contact-us",
      isButton: true,
      variant: "default",
    },
  ],

  className,
}) {
  const { user, logout } = useAuth();

  return (
    <header className={cn("sticky top-0 z-50", className)}>
      <Container>
        <div className="bg-background/15 absolute left-0 h-16 w-full backdrop-blur-lg"></div>
        <div className="relative mx-auto flex h-16 items-center justify-between px-4">
          {/* Logo and Brand */}
          <div className="flex items-center gap-2">
            <Link
              to={logoHref}
              className="flex items-center gap-2 text-xl font-bold"
            >
              {logo}
              {logoTitle}
            </Link>

            {/* Desktop Navigation */}
            <NavigationMenu className="hidden md:flex ml-6">
              <NavigationMenuList>
                {menuItems.map((item, index) => (
                  <NavigationMenuItem key={index}>
                    {item.isLink ? (
                      <NavigationMenuLink asChild>
                        <Link
                          to={item.href}
                          className={navigationMenuTriggerStyle()}
                        >
                          {item.title}
                        </Link>
                      </NavigationMenuLink>
                    ) : (
                      <>
                        <NavigationMenuTrigger>
                          {item.title}
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          {item.content === "overview" ? (
                            <ul className="grid gap-3 p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">
                              <li className="row-span-3">
                                <NavigationMenuLink asChild>
                                  {logoHref.startsWith("http") ? (
                                    <a
                                      className="from-muted/30 to-muted/10 flex h-full w-full flex-col justify-end rounded-md bg-linear-to-b p-6 no-underline outline-hidden select-none focus:shadow-md"
                                      href={logoHref}
                                    >
                                      {logo}
                                      <div className="mt-4 mb-2 text-lg font-medium">
                                        {logoTitle}
                                      </div>
                                      <p className="text-muted-foreground text-sm leading-tight">
                                        {logoDescription}
                                      </p>
                                    </a>
                                  ) : (
                                    <Link
                                      to={logoHref}
                                      className="from-muted/30 to-muted/10 flex h-full w-full flex-col justify-end rounded-md bg-linear-to-b p-6 no-underline outline-hidden select-none focus:shadow-md"
                                    >
                                      {logo}
                                      <div className="mt-4 mb-2 text-lg font-medium">
                                        {logoTitle}
                                      </div>
                                      <p className="text-muted-foreground text-sm leading-tight">
                                        {logoDescription}
                                      </p>
                                    </Link>
                                  )}
                                </NavigationMenuLink>
                              </li>
                              {overviewItems.map((item, i) => (
                                <ListItem
                                  key={i}
                                  href={item.href}
                                  title={item.title}
                                >
                                  {item.description}
                                </ListItem>
                              ))}
                            </ul>
                          ) : item.content === "teachers" ? (
                            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                              {teacherItems.map((component) => (
                                <ListItem
                                  key={component.title}
                                  title={component.title}
                                  href={component.href}
                                >
                                  {component.description}
                                </ListItem>
                              ))}
                            </ul>
                          ) : item.content === "students" ? (
                            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                              {studentItems.map((component) => (
                                <ListItem
                                  key={component.title}
                                  title={component.title}
                                  href={component.href}
                                >
                                  {component.description}
                                </ListItem>
                              ))}
                            </ul>
                          ) : item.content === "parents" ? (
                            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2 lg:w-[600px]">
                              {parentItems.map((component) => (
                                <ListItem
                                  key={component.title}
                                  title={component.title}
                                  href={component.href}
                                >
                                  {component.description}
                                </ListItem>
                              ))}
                            </ul>
                          ) : (
                            item.content
                          )}
                        </NavigationMenuContent>
                      </>
                    )}
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Right side actions */}
          <div className="flex items-center gap-4">
            {user ? (
              <div className="hidden md:flex items-center gap-4">
                {/* Only show actions except 'Sign In' and 'Contact us'/'Book a Demo' when logged in */}
                {actions
                  .filter(
                    (action) =>
                      action.text !== "Sign In" &&
                      action.text !== "Contact us" &&
                      action.text !== "Book a Demo"
                  )
                  .map((action, index) =>
                    action.isButton ? (
                      <Button
                        key={index}
                        variant={action.variant || "default"}
                        className="hidden md:block"
                        asChild
                      >
                        <Link to={action.href}>
                          {action.icon}
                          {action.text}
                          {action.iconRight}
                        </Link>
                      </Button>
                    ) : null
                  )}
                {/* Dashboard button for logged in user */}
                <Button variant="default" className="hidden md:block" asChild>
                  <Link to="/dashboard">Dashboard</Link>
                </Button>
                {/* User Avatar with Dropdown */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      className="relative h-8 w-8 rounded-full"
                    >
                      <Avatar>
                        <AvatarImage
                          src={user.image}
                          alt={user.name || "User"}
                        />
                        <AvatarFallback>
                          {user.name ? (
                            user.name.charAt(0).toUpperCase()
                          ) : (
                            <User className="h-4 w-4" />
                          )}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end" forceMount>
                    <DropdownMenuLabel className="font-normal">
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {user.name || "User"}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {user.email || ""}
                        </p>
                      </div>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link to="/dashboard">Dashboard</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/dashboard/profile">Profile</Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/dashboard/settings">Settings</Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={logout}>
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            ) : (
              <>
                {actions.map((action, index) =>
                  action.isButton ? (
                    <Button
                      key={index}
                      variant={action.variant || "default"}
                      className="hidden md:block"
                      asChild
                    >
                      <Link to={action.href}>
                        {action.icon}
                        {action.text}
                        {action.iconRight}
                      </Link>
                    </Button>
                  ) : (
                    <Link
                      key={index}
                      to={action.href}
                      className="hidden text-sm font-medium md:block"
                    >
                      {action.text}
                    </Link>
                  )
                )}
              </>
            )}

            {/* Mobile menu */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon" className="md:hidden">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle navigation menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left">
                <SheetHeader>
                  <SheetTitle>{logoTitle}</SheetTitle>
                  <SheetDescription>
                    School management system navigation
                  </SheetDescription>
                </SheetHeader>
                <nav className="m-8 grid gap-6 text-lg font-medium">
                  {user && (
                    <div className="flex items-center gap-3 mb-2">
                      <Avatar>
                        <AvatarImage
                          src={user.image}
                          alt={user.name || "User"}
                        />
                        <AvatarFallback>
                          {user.name ? (
                            user.name.charAt(0).toUpperCase()
                          ) : (
                            <User className="h-4 w-4" />
                          )}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="text-sm font-medium">
                          {user.name || "User"}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {user.email || ""}
                        </p>
                      </div>
                    </div>
                  )}

                  {mobileLinks.map((link, index) => (
                    <Link
                      key={index}
                      to={link.href}
                      className="text-muted-foreground hover:text-foreground"
                    >
                      {link.text}
                    </Link>
                  ))}

                  <div className="mt-6 flex flex-col gap-4">
                    {user ? (
                      <>
                        {actions.map((action, index) =>
                          action.text !== "Sign In" ? (
                            <Button
                              key={index}
                              variant={
                                action.isButton
                                  ? action.variant || "default"
                                  : "ghost"
                              }
                              asChild
                            >
                              <Link to={action.href}>{action.text}</Link>
                            </Button>
                          ) : null
                        )}
                        <Button variant="outline" onClick={logout}>
                          Sign out
                        </Button>
                      </>
                    ) : (
                      <>
                        {actions.map((action, index) => (
                          <Button
                            key={index}
                            variant={
                              action.isButton
                                ? action.variant || "default"
                                : "ghost"
                            }
                            asChild
                          >
                            <Link to={action.href}>{action.text}</Link>
                          </Button>
                        ))}
                      </>
                    )}
                  </div>
                </nav>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </Container>
    </header>
  );
}

function ListItem({ className, title, children, href, ...props }) {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          to={href}
          className={cn(
            "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground block space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors select-none",
            className
          )}
          {...props}
        >
          <div className="text-sm font-medium leading-none">{title}</div>
          <p className="text-muted-foreground line-clamp-2 text-sm leading-snug mt-1">
            {children}
          </p>
        </Link>
      </NavigationMenuLink>
    </li>
  );
}
