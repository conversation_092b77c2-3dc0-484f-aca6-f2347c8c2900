import axiosInstance from "./axios-instance";

export const createSchoolAdmin = async (adminData) => {
  try {
    const response = await axiosInstance.post(
      "/school-admins/create",
      adminData
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error creating school admin:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create school admin");
  }
};

export const getSchoolAdmins = async (schoolId = null) => {
  try {
    const url = schoolId
      ? `/school-admins?schoolId=${schoolId}`
      : "/school-admins";
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching school admins:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch school admins");
  }
};

export const getSchoolAdminById = async (id) => {
  try {
    const response = await axiosInstance.get(`/school-admins/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching school admin with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch school admin with ID ${id}`)
    );
  }
};

export const updateSchoolAdmin = async (id, adminData) => {
  try {
    const response = await axiosInstance.put(`/school-admins/${id}`, adminData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating school admin with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update school admin with ID ${id}`)
    );
  }
};

export const deleteSchoolAdmin = async (id) => {
  try {
    const response = await axiosInstance.delete(`/school-admins/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting school admin with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete school admin with ID ${id}`)
    );
  }
};
