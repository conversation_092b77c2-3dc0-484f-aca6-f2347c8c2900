import React from "react";
import { <PERSON><PERSON>elp } from "lucide-react";
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

export function TextInput({
  form,
  name,
  label,
  icon: Icon,
  placeholder,
  description,
  toolTipText,
  type = "text",
  validation = {},
  inputProps = {},
}) {
  return (
    <FormField
      control={form.control}
      name={name}
      rules={validation}
      render={({ field }) => (
        <FormItem>
          <FormLabel className="flex items-center gap-2">
            {label}
            {toolTipText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon" className="h-4 w-4 p-0">
                      <CircleHelp className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-sm">{toolTipText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </FormLabel>
          <div className="relative">
            {Icon && (
              <div className="absolute top-4 left-3 pointer-events-none">
                <Icon className="h-5 w-5" />
              </div>
            )}
          </div>
          <FormControl>
            <Input
              type={type}
              placeholder={placeholder}
              className={Icon ? "pl-10" : ""}
              {...field}
              value={field.value ?? ""}
              {...inputProps}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
