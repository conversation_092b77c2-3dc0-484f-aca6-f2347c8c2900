import React from "react";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
export function FormCard({
  title,
  icon: Icon,
  children,
  className = "",
  headerClassName = "",
  contentClassName = "",
}) {
  return (
    <Card className={`pt-0 ${className}`}>
      <CardHeader
        className={`p-6 border-b bg-muted rounded-lg ${headerClassName}`}
      >
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Icon className="w-5 h-5 text-primary" />
          <span>{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent className={contentClassName}>{children}</CardContent>
    </Card>
  );
}
