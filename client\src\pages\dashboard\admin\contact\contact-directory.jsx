import { useEffect } from "react";
import { useContact } from "@/context/contact-context";
import {
  Bell<PERSON><PERSON>,
  Clock,
  MessageSquareText,
  PlusCircle,
  Users,
} from "lucide-react";
import { StatCard } from "@/components/dashboard/stat-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { ContactColumns } from "@/pages/dashboard/admin/contact/contact-columns";
import { DataTable } from "@/components/data-table/data-table-component/data-table";
import { DataTableSkeleton } from "@/components/data-table/data-table-skeleton/data-table-skeleton";

const ContactDirectory = () => {
  const { contacts, isLoading, fetchAllContacts } = useContact();

  useEffect(() => {
    fetchAllContacts();
  }, []);

  return (
    <div className="flex flex-col">
      <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
        <PageHeader
          isLoading={isLoading}
          title="Contact Management"
          breadcrumbs={[
            { label: "Dashboard", href: "/dashboard" },
            { label: "Contacts" },
          ]}
          actions={[
            {
              label: "New Contact",
              icon: PlusCircle,
              href: "/contact-us",
            },
          ]}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatCard
            title="Total Contacts"
            value={contacts.length}
            description="All time"
            icon={Users}
            isLoading={isLoading}
            trend="positive"
          />

          <StatCard
            title="Unread Contacts"
            value={contacts.filter((contact) => contact.status == "new").length}
            description="Require attention"
            icon={BellRing}
            isLoading={isLoading}
            trend="warning"
          />

          <StatCard
            title="Recent Contact"
            value={
              contacts.filter((contact) => {
                const createdAt = new Date(contact.createdAt);
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                return createdAt >= thirtyDaysAgo;
              }).length
            }
            description="Submitted in last 30 days"
            icon={Clock}
            isLoading={isLoading}
          />

          <StatCard
            title="Top Inquiry Type"
            value={(() => {
              if (contacts.length === 0) return "N/A";
              const sourceCounts = contacts.reduce((acc, contact) => {
                const source = contact.inquiryType || "Unknown";
                acc[source] = (acc[source] || 0) + 1;
                return acc;
              }, {});
              let topSource = "Unknown";
              let maxCount = 0;
              for (const [source, count] of Object.entries(sourceCounts)) {
                if (count > maxCount) {
                  maxCount = count;
                  topSource = source;
                }
              }

              return topSource;
            })()}
            description="Most common referral source"
            icon={MessageSquareText}
            isLoading={isLoading}
          />
        </div>
        <div>
          {isLoading ? (
            <DataTableSkeleton />
          ) : (
            <DataTable
              data={contacts}
              columns={ContactColumns}
              model="contact"
            />
          )}
        </div>
      </main>
    </div>
  );
};

export default ContactDirectory;
