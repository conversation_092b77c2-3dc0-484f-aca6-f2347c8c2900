import Contact from "../models/contact.model.js";

export const submitContactForm = async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      schoolName,
      schoolEmail,
      schoolWebsite,
      role,
      schoolSize,
      inquiryType,
      message,
      newsletter,
    } = req.body;

    if (!name || !email || !inquiryType || !message) {
      return res.status(400).json({
        success: false,
        message: "Name, email, inquiry type, and message are required fields.",
      });
    }

    const newContactSubmission = new Contact({
      name,
      email,
      phone,
      schoolName,
      schoolEmail,
      schoolWebsite,
      role,
      schoolSize,
      inquiryType,
      message,
      newsletter,
    });

    await newContactSubmission.save();

    res.status(201).json({
      success: true,
      message:
        "Contact form submitted successfully. We will get back to you shortly.",
      data: newContactSubmission,
    });
  } catch (error) {
    console.error("Contact Form Submission Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error. Please try again later.",
    });
  }
};

export const getAllContactSubmissions = async (req, res) => {
  try {
    const submissions = await Contact.find().sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      count: submissions.length,
      data: submissions,
    });
  } catch (error) {
    console.error("Get All Contact Submissions Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const getContactSubmissionById = async (req, res) => {
  try {
    const submission = await Contact.findById(req.params.id);
    if (!submission) {
      return res.status(404).json({
        success: false,
        message: "Contact submission not found.",
      });
    }
    res.status(200).json({
      success: true,
      data: submission,
    });
  } catch (error) {
    console.error("Get Contact Submission By ID Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};

export const updateContactSubmissionStatus = async (req, res) => {
  try {
    const { status } = req.body;
    if (!status) {
      return res.status(400).json({
        success: false,
        message: "Status is required.",
      });
    }

    const submission = await Contact.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true, runValidators: true }
    );

    if (!submission) {
      return res.status(404).json({
        success: false,
        message: "Contact submission not found.",
      });
    }

    res.status(200).json({
      success: true,
      message: "Contact submission status updated successfully.",
      data: submission,
    });
  } catch (error) {
    console.error("Update Contact Submission Status Error:", error);
    res.status(500).json({
      success: false,
      message: "Internal server error.",
    });
  }
};
