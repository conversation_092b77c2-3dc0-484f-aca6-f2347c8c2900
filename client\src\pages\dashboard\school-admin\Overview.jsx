import React from "react";
import { Users, BookOpen, Calendar, GraduationCap } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";
import { ChartAreaInteractive } from "@/components/dashboard/chart-area-interactive";

const SchoolAdminOverview = () => {
  const { user, isLoading } = useAuth();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="School Admin Dashboard"
        subtitle="Manage your school's students, teachers, and academic programs"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Students"
          value="1,245"
          description="+12% from last month"
          icon={Users}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="Total Teachers"
          value="68"
          description="+2 from last month"
          icon={GraduationCap}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="Active Classes"
          value="42"
          description="+4 from last month"
          icon={BookOpen}
          isLoading={isLoading}
          trend="positive"
        />

        <StatCard
          title="Upcoming Events"
          value="8"
          description="This week"
          icon={Calendar}
          isLoading={isLoading}
        />
      </div>

      <ChartAreaInteractive />
    </div>
  );
};

export default SchoolAdminOverview;
