import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { signInSchema } from "@/schema/auth";
import { TextInput } from "@/components/form-inputs/text-input";
import { PasswordInput } from "@/components/form-inputs/password-input";
import { AtSignIcon, LockIcon, LogIn } from "lucide-react";
import { SubmitButton } from "@/components/form-inputs/submit-button";
import { toast } from "sonner";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/auth-context";

export function SignInForm() {
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();
  const { login } = useAuth();

  const form = useForm({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleSubmit = async (data) => {
    setIsLoading(true);

    try {
      await login(data);
      toast.success("Logged In Successfully", {
        description: "You are now logged in",
      });
      navigate("/dashboard");
    } catch (error) {
      console.error("Sign in error:", error);
      toast.error(error.message || "Failed to sign in");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8 w-full max-w-md">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <TextInput
            form={form}
            name="email"
            label="Email"
            placeholder="Enter your email"
            icon={AtSignIcon}
          />

          <PasswordInput
            form={form}
            name="password"
            label="Password"
            placeholder="Enter your password"
            forgotPasswordLink="/forgot-password"
            icon={LockIcon}
          />

          <SubmitButton
            buttonIcon={LogIn}
            title="Sign In"
            loading={isLoading}
            loadingTitle="Signing in..."
            className="w-full mt-6"
          />

          <div className="text-center">
            <span className="text-sm text-muted-foreground">
              Don't have an account?{" "}
            </span>
            <a href="/sign-up" className="text-sm text-primary hover:underline">
              Sign up
            </a>
          </div>
        </form>
      </Form>
    </div>
  );
}
