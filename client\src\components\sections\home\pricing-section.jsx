import { cn } from "@/lib/utils";
import { Section } from "@/components/ui/section";
import { School, Building2, Building } from "lucide-react";
import { PricingColumn } from "@/components/ui/pricing-column";
import { Container } from "@/components/ui/container";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";

export default function Pricing({
  title = "Choose the Right Plan for Your Institution",
  description = "Flexible pricing options designed to meet the needs of educational institutions of all sizes.",
  badge = (
    <Announcement>
      <AnnouncementTag>PRICING</AnnouncementTag>
      <AnnouncementTitle>Transparent and Flexible</AnnouncementTitle>
    </Announcement>
  ),
  plans = [
    {
      name: "Basic",
      icon: <School className="size-4" />,
      description: "Perfect for small schools and educational startups",
      price: 199,
      priceNote: "per month, billed annually",
      cta: {
        variant: "glow",
        label: "Start with Basic",
        href: "/signup/basic",
      },
      features: [
        "Up to 500 student accounts",
        "10 teacher/admin accounts",
        "Core attendance & grading features",
        "Basic reporting tools",
        "Email support",
      ],
      variant: "default",
    },
    {
      name: "Standard",
      icon: <Building2 className="size-4" />,
      description:
        "Ideal for medium-sized schools and educational institutions",
      price: 399,
      priceNote: "per month, billed annually",
      cta: {
        variant: "default",
        label: "Choose Standard",
        href: "/signup/standard",
      },
      features: [
        "Up to 2,000 student accounts",
        "Unlimited teacher/admin accounts",
        "Advanced attendance & grading",
        "Comprehensive reporting & analytics",
        "Parent portal access",
        "Priority support",
      ],
      variant: "glow-brand",
    },
    {
      name: "Enterprise",
      icon: <Building className="size-4" />,
      description: "For large schools, districts, and educational networks",
      price: 999,
      priceNote: "per month, billed annually",
      cta: {
        variant: "default",
        label: "Contact Sales",
        href: "/contact-sales",
      },
      features: [
        "Unlimited student accounts",
        "District-wide management",
        "Advanced security features",
        "Custom integrations",
        "API access",
        "Dedicated account manager",
        "24/7 premium support",
      ],
      variant: "glow",
    },
  ],

  className = "",
}) {
  return (
    <Section className={cn(className)}>
      <Container className="gap-12">
        {(title || description) && (
          <div className="flex flex-col items-center gap-4 px-4 text-center sm:gap-8">
            {badge}{" "}
            {title && (
              <h2 className="text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
                {title}
              </h2>
            )}
            {description && (
              <p className="text-md text-muted-foreground max-w-[600px] font-medium sm:text-xl">
                {description}
              </p>
            )}
          </div>
        )}
        {plans !== false && plans.length > 0 && (
          <div className="max-w-container mx-auto mt-8 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {plans.map((plan) => (
              <PricingColumn
                key={plan.name}
                name={plan.name}
                icon={plan.icon}
                description={plan.description}
                price={plan.price}
                priceNote={plan.priceNote}
                cta={plan.cta}
                features={plan.features}
                variant={plan.variant}
                className={plan.className}
              />
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}
