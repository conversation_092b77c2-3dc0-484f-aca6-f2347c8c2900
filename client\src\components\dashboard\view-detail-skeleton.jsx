import { Container } from "@/components/ui/container";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { InfoCard } from "@/components/dashboard/info-card";
import { PageHeader } from "@/components/dashboard/page-header";
import { ProfileBanner } from "@/components/dashboard/profile-banner";
import { User, Shield, FileText, MapPin, Phone } from "lucide-react";

export const ViewDetailSkeleton = () => {
  const sidebarCards = [
    { id: "personal", icon: User, title: "Personal Info" },
    { id: "identity", icon: Shield, title: "Identity Documents" },
    { id: "system", icon: Shield, title: "System Access" },
    { id: "notes", icon: FileText, title: "Additional Notes" },
  ];

  const tabs = [
    { id: "personal", label: "Personal" },
    { id: "professional", label: "Professional" },
    { id: "additional", label: "Additional" },
    { id: "children", label: "Children" },
    { id: "system", label: "System" },
  ];

  return (
    <Container className="py-8">
      <div className="space-y-6">
        <PageHeader
          isLoading={true}
          title="Loading..."
          breadcrumbs={[{}, {}]}
          actions={[{}, {}]}
        />
        <ProfileBanner isLoading={true} />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Left Sidebar */}
          <div className="md:col-span-1">
            <div className="space-y-4">
              {sidebarCards.map(({ id, icon: Icon, title }) => (
                <InfoCard key={id} icon={Icon} title={title} isLoading={true}>
                  <div className="space-y-4" />
                </InfoCard>
              ))}
            </div>
          </div>

          {/* Right Content Area */}
          <div className="md:col-span-2">
            <Tabs defaultValue="personal" className="w-full">
              {/* Tabs List */}
              <div className="bg-card rounded-lg border shadow-sm p-0.5 mb-3">
                <TabsList className="grid w-full grid-cols-5 bg-transparent">
                  {tabs.map(({ id }) => (
                    <TabsTrigger key={id} value={id} disabled>
                      <Skeleton className="h-4 w-4 rounded" />
                      <Skeleton className="h-4 w-16 ml-2 hidden sm:inline-block" />
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>

              <TabsContent value="personal" className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={User}
                      title="Personal Details"
                      isLoading={true}
                    >
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4" />
                    </InfoCard>
                  </div>
                  <InfoCard
                    icon={Phone}
                    title="Contact Information"
                    isLoading={true}
                  >
                    <div className="space-y-4" />
                  </InfoCard>
                  <InfoCard
                    icon={Shield}
                    title="Identity Documents"
                    isLoading={true}
                  >
                    <div className="space-y-4" />
                  </InfoCard>
                  <div className="lg:col-span-2">
                    <InfoCard
                      icon={MapPin}
                      title="Address Information"
                      isLoading={true}
                      className="lg:col-span-2"
                    >
                      <div className="space-y-4" />
                    </InfoCard>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Container>
  );
};
