import { createContext, useContext, useState, useEffect } from "react";
import {
  createSchool,
  getSchools,
  getSchoolById,
  updateSchool,
  deleteSchool,
  updateSchoolStatus,
} from "@/api/school-api";

const SchoolContext = createContext({
  schools: [],
  currentSchool: null,
  isLoading: false,
  error: null,
  addSchool: () => {},
  fetchAllSchools: () => {},
  fetchSchoolById: () => {},
  editSchool: () => {},
  removeSchool: () => {},
  updateStatus: () => {},
});

export const SchoolProvider = ({ children }) => {
  const [schools, setSchools] = useState([]);
  const [currentSchool, setCurrentSchool] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [schoolOptions, setSchoolOptions] = useState([]);

  useEffect(() => {
    const options = schools.map((school) => ({
      label: school.name,
      value: { id: school._id, name: school.name },
    }));
    setSchoolOptions(options);
  }, [schools]);

  const addSchool = async (schoolData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createSchool(schoolData);
      setSchools((prevSchools) => [...prevSchools, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create school");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllSchools = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSchools();
      setSchools(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch schools");
      setSchools([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSchoolById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      try {
        const data = await getSchoolById(id);
        const schoolData = data.school || data;
        if (schoolData) {
          setCurrentSchool(schoolData);
          setIsLoading(false);
          return schoolData;
        }
      } catch (apiError) {
        console.error("API Error, using mock data:", apiError);
        throw apiError;
      }
    } catch (error) {
      setError(error.message || `Failed to fetch school with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editSchool = async (id, schoolData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSchool(id, schoolData);
      setSchools(
        schools.map((school) =>
          school._id === id ? { ...school, ...response.data } : school
        )
      );
      if (currentSchool && currentSchool._id === id) {
        setCurrentSchool({ ...currentSchool, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update school with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeSchool = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteSchool(id);
      setSchools(schools.filter((school) => school._id !== id));
      if (currentSchool && currentSchool._id === id) {
        setCurrentSchool(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete school with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, statusInfo) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await updateSchoolStatus(id, statusInfo.status);
      setSchools(
        schools.map((school) =>
          school._id === id ? { ...school, status: data.school.status } : school
        )
      );
      if (currentSchool && currentSchool._id === id) {
        setCurrentSchool({ ...currentSchool, status: data.school.status });
      }
      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to update status for school with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <SchoolContext.Provider
      value={{
        schools,
        setSchools,
        currentSchool,
        isLoading,
        error,
        addSchool,
        fetchAllSchools,
        fetchSchoolById,
        editSchool,
        removeSchool,
        updateStatus,
        schoolOptions,
      }}
    >
      {children}
    </SchoolContext.Provider>
  );
};

export const useSchool = () => useContext(SchoolContext);
