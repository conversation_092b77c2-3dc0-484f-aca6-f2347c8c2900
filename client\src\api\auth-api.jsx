import axiosInstance from "./axios-instance";

// Sign In API
export const signin = async (credentials) => {
  try {
    const response = await axiosInstance.post("/auth/signin", credentials);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Invalid email or password"
    );
  }
};

// Sign Up API
export const signup = async (userData) => {
  try {
    const response = await axiosInstance.post("/auth/signup", userData);
    return response.data;
  } catch (error) {
    throw new Error(
      error.response?.data?.message || "Sign up failed. Please try again."
    );
  }
};

// Log Out API
export const logout = async () => {
  try {
    await axiosInstance.post("/auth/signout");
  } catch (error) {
    throw new Error("Logout failed. Please try again.");
  }
};

// Get Profile API
export const getProfile = async () => {
  try {
    const response = await axiosInstance.get("/auth/profile");
    return response.data;
  } catch (error) {
    throw new Error("Failed to get user profile. Please try again.");
  }
};
