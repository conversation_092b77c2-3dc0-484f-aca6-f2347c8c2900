import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";

export const FormCardSkeleton = () => {
  return (
    <Card className="pt-0 mt-6">
      <CardHeader className="p-6 border-b bg-muted rounded-lg">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <Skeleton className="w-5 h-5" />
          <Skeleton className="h-5 w-32" />
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <div className="bg-secondary border border-border rounded-lg p-3">
              <Skeleton className="h-5 w-32" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-36" />
            <div className="bg-secondary border border-border rounded-lg p-3">
              <Skeleton className="h-5 w-28" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <div className="bg-secondary border border-border rounded-lg p-3 flex justify-between items-center">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-4" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <div className="bg-secondary border border-border rounded-lg p-3 flex items-center gap-2">
              <Skeleton className="h-4 w-4" />
              <Skeleton className="h-5 w-28" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-32" />
            <div className="bg-secondary border border-border rounded-lg p-3 flex justify-between items-center">
              <Skeleton className="h-5 w-20" />
              <Skeleton className="h-4 w-4" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-16" />
            <div className="bg-secondary border border-border rounded-lg p-3">
              <Skeleton className="h-5 w-56" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-28" />
            <div className="bg-secondary border border-border rounded-lg p-3">
              <Skeleton className="h-5 w-48" />
            </div>
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-28" />
            <div className="bg-secondary border border-border rounded-lg p-3 flex gap-2">
              <div className="flex items-center gap-1 bg-muted rounded px-2 py-1">
                <Skeleton className="h-4 w-6" />
                <Skeleton className="h-4 w-8" />
                <Skeleton className="h-3 w-3" />
              </div>
              <Skeleton className="h-5 w-24 flex-1" />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
