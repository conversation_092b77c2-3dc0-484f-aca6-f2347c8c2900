import express from "express";
import {
  getAdminDashboardStats,
  getSchoolAdminDashboardStats,
  getAdminChartData,
} from "../controllers/dashboard.controller.js";
import { protect, authorize } from "../middleware/auth.middleware.js";

const router = express.Router();

router.get(
  "/admin/stats",
  protect,
  authorize(["admin"]),
  getAdminDashboardStats
);

router.get(
  "/school-admin/stats",
  protect,
  authorize(["school-admin"]),
  getSchoolAdminDashboardStats
);

router.get(
  "/admin/chart-data",
  protect,
  authorize(["admin"]),
  getAdminChartData
);

export default router;
