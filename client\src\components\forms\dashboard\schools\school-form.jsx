import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Form } from "@/components/ui/form";
import { TextInput } from "@/components/form-inputs/text-input";
import { DateInput } from "@/components/form-inputs/date-input";
import { TextareaInput } from "@/components/form-inputs/textarea-input";
import { SelectInput } from "@/components/form-inputs/select-input";
import { PhoneInput } from "@/components/form-inputs/phone-input";
import { FileInput } from "@/components/form-inputs/file-input";
import { CheckboxInput } from "@/components/form-inputs/checkbox-input";
import {
  Building2,
  ImagePlus,
  MapPin,
  School,
  Settings,
  UserCheck,
  CreditCard,
  Shield,
} from "lucide-react";
import {
  affiliations,
  boards,
  paymentMethods,
  schoolTypes,
  subscriptionDurations,
} from "@/utils/form-options";
import { FormCard } from "@/components/forms/form-card";
import { FormFooter } from "@/components/forms/form-footer";
import { useSchool } from "@/context/school-context";
import { useNavigate } from "react-router-dom";

export function SchoolForm({ editingId, initialData }) {
  const { addSchool, editSchool } = useSchool();

  const form = useForm({
    defaultValues: {
      // Basic Information
      name: initialData?.name || "",
      registrationNumber: initialData?.registrationNumber || "",
      type: initialData?.type || "",
      establishedDate: initialData?.establishedDate || "",
      board: initialData?.board || "",
      website: initialData?.website || "",
      email: initialData?.email || "",
      phone: initialData?.phone || "",
      description: initialData?.description || "",

      // Address Information
      city: initialData?.city || "",
      state: initialData?.state || "",
      country: initialData?.country || "",
      postalCode: initialData?.postalCode || "",
      address: initialData?.address || "",

      // Administrative Information
      principalName: initialData?.principalName || "",
      principalEmail: initialData?.principalEmail || "",
      principalPhone: initialData?.principalPhone || "",
      affiliationNumber: initialData?.affiliationNumber || "",
      affiliationBody: initialData?.affiliationBody || "",
      taxId: initialData?.taxId || "",
      vision: initialData?.vision || "",
      mission: initialData?.mission || "",

      // Facilities & Features
      numberOfClassrooms: initialData?.numberOfClassrooms || "",
      numberOfTeachers: initialData?.numberOfTeachers || "",
      numberOfStudents: initialData?.numberOfStudents || "",
      hasLibrary: initialData?.hasLibrary || false,
      hasComputerLab: initialData?.hasComputerLab || false,
      hasScienceLab: initialData?.hasScienceLab || false,
      hasSportsFacilities: initialData?.hasSportsFacilities || false,
      hasCafeteria: initialData?.hasCafeteria || false,
      hasTransportation: initialData?.hasTransportation || false,
      hasMedicalRoom: initialData?.hasMedicalRoom || false,
      hasAuditorium: initialData?.hasAuditorium || false,
      hasSmartClassrooms: initialData?.hasSmartClassrooms || false,
      additionalFacilities: initialData?.additionalFacilities || "",

      // Media & Documents
      schoolLogo: initialData?.schoolLogo || "",
      registrationDocument: initialData?.registrationDocument || "",

      // Subscription Information
      subscriptionPlan: initialData?.subscriptionPlan || "",
      subscriptionStartDate: initialData?.subscriptionStartDate || "",
      subscriptionDuration: initialData?.subscriptionDuration || "",
      paymentMethod: initialData?.paymentMethod || "",

      // Management Features
      hasStudentManagement: initialData?.hasStudentManagement || false,
      hasStaffManagement: initialData?.hasStaffManagement || false,
      hasAttendanceTracking: initialData?.hasAttendanceTracking || false,
      hasFeeManagement: initialData?.hasFeeManagement || false,
      hasExamManagement: initialData?.hasExamManagement || false,
      hasTransportManagement: initialData?.hasTransportManagement || false,
      hasHostelManagement: initialData?.hasHostelManagement || false,
      hasInventoryManagement: initialData?.hasInventoryManagement || false,
      hasAdvancedReports: initialData?.hasAdvancedReports || false,
    },
  });

  const navigate = useNavigate();

  const handleSubmit = async (data) => {
    try {
      if (editingId) {
        await editSchool(editingId, data);
        navigate("/dashboard/schools");
        toast.success("School updated successfully!");
      } else {
        await addSchool(data);
        toast.success("School created successfully!");
      }
    } catch (error) {
      console.error("School form submission error:", error);
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to save school. Please try again."
      );
    }
  };

  return (
    <div className="pt-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <div className="grid grid-cols-12 gap-6">
            <div className="lg:col-span-12 col-span-full space-y-6">
              <FormCard title="Basic Information" icon={School}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="name"
                    label="School Name"
                    placeholder="Enter the name of the school"
                    validation={{ required: "School name is required" }}
                  />
                  <TextInput
                    form={form}
                    name="registrationNumber"
                    label="Registration Number"
                    placeholder="Enter the registration number of the school"
                    validation={{ required: "Registration number is required" }}
                  />
                  <SelectInput
                    form={form}
                    label="School Type"
                    name="type"
                    placeholder="Select school type"
                    options={schoolTypes}
                    validation={{ required: "School type is required" }}
                  />
                  <DateInput
                    form={form}
                    name="establishedDate"
                    label="Established Date"
                    placeholder="YYYY-MM-DD"
                    validation={{ required: "Established date is required" }}
                  />
                  <SelectInput
                    form={form}
                    label="Board/Curriculum"
                    name="board"
                    placeholder="Select board or curriculum"
                    options={boards}
                    validation={{ required: "Board/Curriculum is required" }}
                  />
                  <TextInput
                    form={form}
                    label="Website"
                    name="website"
                    placeholder="https://www.schoolwebsite.com"
                  />
                  <TextInput
                    form={form}
                    label="Email Address"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    validation={{
                      required: "Email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    label="Phone Number"
                    name="phone"
                    validation={{ required: "Please enter valid phone number" }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      label="School Description"
                      name="description"
                      placeholder="Brief description of the school"
                      validation={{ required: "Description is required" }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Address Information" icon={MapPin}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="city"
                    label="City"
                    placeholder="Enter the city"
                    validation={{ required: "City is required" }}
                  />
                  <TextInput
                    form={form}
                    name="state"
                    label="State"
                    placeholder="Enter the state"
                    validation={{ required: "State is required" }}
                  />
                  <TextInput
                    form={form}
                    name="country"
                    label="Country"
                    placeholder="Enter the country"
                    validation={{ required: "Country is required" }}
                  />
                  <TextInput
                    form={form}
                    name="postalCode"
                    label="Postal Code"
                    placeholder="Enter the postal code"
                    validation={{ required: "Postal code is required" }}
                  />
                  <div className="md:col-span-2">
                    <TextareaInput
                      form={form}
                      name="address"
                      label="School Address"
                      placeholder="Enter the address of the school"
                      validation={{ required: "School address is required" }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Administrative Information" icon={UserCheck}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    form={form}
                    name="principalName"
                    label="Principal Name"
                    placeholder="Enter the name of the principal"
                    validation={{ required: "Principal name is required" }}
                  />
                  <TextInput
                    form={form}
                    name="principalEmail"
                    label="Principal Email"
                    type="email"
                    placeholder="Enter the email of the principal"
                    validation={{
                      required: "Principal email is required",
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: "Invalid email address",
                      },
                    }}
                  />
                  <PhoneInput
                    form={form}
                    name="principalPhone"
                    label="Principal Phone"
                    validation={{
                      required: "Principal phone is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="affiliationNumber"
                    label="Affiliation Number"
                    placeholder="Enter the affiliation number"
                    validation={{ required: "Affiliation number is required" }}
                  />
                  <SelectInput
                    form={form}
                    label="Affiliated Body"
                    name="affiliationBody"
                    placeholder="Select affiliated body"
                    options={affiliations}
                    validation={{ required: "Affiliated body is required" }}
                  />
                  <TextInput
                    form={form}
                    name="taxId"
                    label="Tax ID"
                    placeholder="Enter the tax ID"
                    validation={{ required: "Tax ID is required" }}
                  />
                  <TextareaInput
                    form={form}
                    name="vision"
                    label="Vision"
                    placeholder="Enter the vision of the school"
                    validation={{ required: "Vision is required" }}
                  />
                  <TextareaInput
                    form={form}
                    name="mission"
                    label="Mission"
                    placeholder="Enter the mission of the school"
                    validation={{ required: "Mission is required" }}
                  />
                </div>
              </FormCard>

              <FormCard title="Facilities & Features" icon={Building2}>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <TextInput
                    form={form}
                    name="numberOfClassrooms"
                    label="Number of Classrooms"
                    type="number"
                    placeholder="Enter the number of classrooms"
                    validation={{
                      required: "Number of classrooms is required",
                    }}
                  />
                  <TextInput
                    form={form}
                    name="numberOfTeachers"
                    label="Number of Teachers"
                    type="number"
                    placeholder="Enter the number of teachers"
                    validation={{ required: "Number of teachers is required" }}
                  />
                  <TextInput
                    form={form}
                    name="numberOfStudents"
                    label="Number of Students"
                    type="number"
                    placeholder="Enter the number of students"
                    validation={{ required: "Number of students is required" }}
                  />
                  <div className="md:col-span-3">
                    <FormCard title="Features" icon={Settings}>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 pb-4">
                        <CheckboxInput
                          form={form}
                          label="Library"
                          name="hasLibrary"
                        />
                        <CheckboxInput
                          form={form}
                          label="Computer Lab"
                          name="hasComputerLab"
                        />
                        <CheckboxInput
                          form={form}
                          label="Science Lab"
                          name="hasScienceLab"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasSportsFacilities"
                          label="Has Sports Facilities"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasCafeteria"
                          label="Has Cafeteria"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasTransportation"
                          label="Has Transportation"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasMedicalRoom"
                          label="Has Medical Room"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasAuditorium"
                          label="Has Auditorium"
                        />
                        <CheckboxInput
                          form={form}
                          name="hasSmartClassrooms"
                          label="Has Smart Classrooms"
                        />
                      </div>
                    </FormCard>
                  </div>
                  <div className="md:col-span-3">
                    <TextareaInput
                      form={form}
                      name="additionalFacilities"
                      label="Additional Facilities"
                      placeholder="Enter any additional facilities"
                      inputProps={{ rows: 3 }}
                    />
                  </div>
                </div>
              </FormCard>

              <FormCard title="Media & Documents" icon={ImagePlus}>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FileInput
                    form={form}
                    name="schoolLogo"
                    label="School Logo"
                    description="Upload school logo (PNG, JPG, JPEG)"
                    accept="image/*"
                  />
                  <FileInput
                    form={form}
                    name="registrationDocument"
                    label="Registration Document"
                    description="Upload school registration certificate"
                    accept=".pdf,.jpg,.jpeg,.png"
                  />
                </div>
              </FormCard>

              <FormCard title="Subscription Information" icon={CreditCard}>
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2 pb-6">
                  <SelectInput
                    form={form}
                    name="subscriptionPlan"
                    label="Subscription Plan"
                    placeholder="Select subscription plan"
                    options={[
                      { label: "Basic", value: "basic" },
                      { label: "Standard", value: "standard" },
                      { label: "Enterprise", value: "enterprise" },
                    ]}
                    validation={{ required: "Subscription plan is required" }}
                  />
                  <DateInput
                    form={form}
                    name="subscriptionStartDate"
                    label="Subscription Start Date"
                    placeholder="Select the subscription start date"
                    validation={{
                      required: "Subscription start date is required",
                    }}
                  />
                  <SelectInput
                    form={form}
                    name="subscriptionDuration"
                    label="Subscription Duration"
                    placeholder="Enter the subscription duration"
                    options={subscriptionDurations}
                    validation={{
                      required: "Subscription duration is required",
                    }}
                  />
                  <SelectInput
                    form={form}
                    name="paymentMethod"
                    label="Payment Method"
                    placeholder="Select payment method"
                    options={paymentMethods}
                    validation={{ required: "Payment method is required" }}
                  />
                </div>
                <FormCard title="Management Features" icon={Shield}>
                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
                    <CheckboxInput
                      form={form}
                      name="hasStudentManagement"
                      label="Has Student Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasStaffManagement"
                      label="Has Staff Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasAttendanceTracking"
                      label="Has Attendance Tracking"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasFeeManagement"
                      label="Has Fee Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasExamManagement"
                      label="Has Exam Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasTransportManagement"
                      label="Has Transport Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasHostelManagement"
                      label="Has Hostel Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasInventoryManagement"
                      label="Has Inventory Management"
                    />
                    <CheckboxInput
                      form={form}
                      name="hasAdvancedReports"
                      label="Has Advanced Reports"
                    />
                  </div>
                </FormCard>
              </FormCard>
            </div>
          </div>
          <FormFooter
            href="/schools"
            parent=""
            title="School"
            editingId={editingId}
          />
        </form>
      </Form>
    </div>
  );
}
