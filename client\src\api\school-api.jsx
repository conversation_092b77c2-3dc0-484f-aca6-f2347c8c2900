import axiosInstance from "./axios-instance";

export const createSchool = async (schoolData) => {
  try {
    const response = await axiosInstance.post("/schools/create", schoolData);
    return response.data;
  } catch (error) {
    console.error(
      "Error creating school:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create school");
  }
};

export const getSchools = async () => {
  try {
    const response = await axiosInstance.get("/schools");
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching schools:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch schools");
  }
};

export const getSchoolById = async (id) => {
  try {
    const response = await axiosInstance.get(`/schools/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching school with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to fetch school with ID ${id}`)
    );
  }
};

export const updateSchool = async (id, schoolData) => {
  try {
    const response = await axiosInstance.put(`/schools/${id}`, schoolData);
    return response.data;
  } catch (error) {
    console.error(
      `Error updating school with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to update school with ID ${id}`)
    );
  }
};

export const deleteSchool = async (id) => {
  try {
    const response = await axiosInstance.delete(`/schools/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting school with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data || new Error(`Failed to delete school with ID ${id}`)
    );
  }
};

export const updateSchoolStatus = async (id, status) => {
  try {
    const response = await axiosInstance.patch(`/schools/${id}/status`, {
      status,
    });
    return response.data;
  } catch (error) {
    console.error(
      `Error updating school status for school with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update school status for school with ID ${id}`)
    );
  }
};
