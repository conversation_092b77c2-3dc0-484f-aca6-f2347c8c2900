import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { CalendarIcon, Search, SlidersHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  IconChevronDown,
  IconChevronLeft,
  IconChevronRight,
  IconChevronsLeft,
  IconChevronsRight,
  IconLayoutColumns,
} from "@tabler/icons-react";
import { addDays, format } from "date-fns";
import { Select, SelectTrigger } from "@/components/ui/select";

export function DataTableSkeleton({ columnCount = 5, rowCount = 5 }) {
  const columns = Array(columnCount).fill(0);
  const rows = Array(rowCount).fill(0);

  const [date, _] = useState({
    from: addDays(new Date(), -30),
    to: new Date(),
  });

  return (
    <>
      {/* Filters */}
      <div className="w-full flex-col justify-start gap-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-2">
          <div className="flex flex-col sm:flex-row gap-2 flex-1 ">
            <div className="flex justify-between items-center w-full max-w-2xl">
              <div className="relative w-full">
                <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                  <Search className="h-4 w-4 text-muted-foreground" />
                </div>
                <Input placeholder="Search..." className="pl-10" disabled />
              </div>
            </div>
          </div>
          <Button variant="outline" className="gap-2" disabled>
            <CalendarIcon className="h-4 w-4" />
            {date?.from ? (
              date.to ? (
                <>
                  {format(date.from, "LLL dd, y")} -{" "}
                  {format(date.to, "LLL dd, y")}
                </>
              ) : (
                format(date.from, "LLL dd, y")
              )
            ) : (
              <span>Pick a date</span>
            )}
          </Button>
          <div className="flex items-center gap-2">
            <Button variant="outline" className="gap-2" disabled>
              <SlidersHorizontal className="h-4 w-4" />
              <span>Life time</span>
              <IconChevronDown />
            </Button>
            <Button variant="outline" className="gap-2" disabled>
              <IconLayoutColumns />
              <span className="hidden lg:inline">Customize Columns</span>
              <span className="lg:hidden">Columns</span>
              <IconChevronDown />
            </Button>
          </div>
        </div>
      </div>
      {/* Table */}
      <div className="overflow-hidden rounded-lg border my-4">
        <Table>
          <TableHeader className="bg-muted sticky top-0 z-10">
            <TableRow>
              {columns.map((_, index) => (
                <TableHead key={`skeleton-header-${index}`}>
                  <Skeleton className="h-8 w-full" />
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {rows.map((_, rowIndex) => (
              <TableRow key={`skeleton-row-${rowIndex}`}>
                {columns.map((_, colIndex) => (
                  <TableCell key={`skeleton-cell-${rowIndex}-${colIndex}`}>
                    <Skeleton className="h-8 w-full" />
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
      {/* Pagination */}
      <div className="flex items-center justify-between my-4">
        <Skeleton className="h-8 w-40" />
        <div className="flex w-full items-center gap-8 lg:w-fit">
          Rows per page
          <Select variant="outline" size="icon" disabled>
            <SelectTrigger className="w-20">
              <Skeleton className="h-6 w-full" />
            </SelectTrigger>
          </Select>
          <span className="flex w-fit items-center justify-center text-sm font-medium">
            Page 1 of 1
          </span>
          <div className="ml-auto flex items-center gap-2 lg:ml-0">
            <Button variant="outline" className="size-8" size="icon" disabled>
              <IconChevronsLeft />
            </Button>
            <Button variant="outline" className="size-8" size="icon" disabled>
              <IconChevronLeft />
            </Button>
            <Button variant="outline" className="size-8" size="icon" disabled>
              <IconChevronRight />
            </Button>
            <Button variant="outline" className="size-8" size="icon" disabled>
              <IconChevronsRight />
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
