import { createContext, useContext, useState, useEffect } from "react";
import {
  createParent,
  getParents,
  getParentById,
  updateParent,
  deleteParent,
  updateParentStatus,
} from "@/api/parent-api";

const ParentContext = createContext({
  parents: [],
  currentParent: null,
  isLoading: false,
  error: null,
  addParent: () => {},
  fetchAllParents: () => {},
  fetchParentById: () => {},
  editParent: () => {},
  removeParent: () => {},
  updateStatus: () => {},
});

export const ParentProvider = ({ children }) => {
  const [parents, setParents] = useState([]);
  const [currentParent, setCurrentParent] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [parentOptions, setParentOptions] = useState([]);

  useEffect(() => {
    const options = parents.map((parent) => ({
      label: `${parent.firstName} ${parent.lastName}`,
      value: { id: parent._id, name: `${parent.firstName} ${parent.lastName}` },
    }));
    setParentOptions(options);
  }, [parents]);

  const addParent = async (parentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createParent(parentData);
      setParents((prevParents) => [...prevParents, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create parent");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllParents = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getParents();
      setParents(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch parents");
      setParents([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchParentById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getParentById(id);
      const parentData = data.data || data;
      setCurrentParent(parentData);
      setIsLoading(false);
      return parentData;
    } catch (error) {
      setError(error.message || `Failed to fetch parent with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editParent = async (id, parentData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateParent(id, parentData);
      setParents(
        parents.map((parent) =>
          parent._id === id ? { ...parent, ...response.data } : parent
        )
      );
      if (currentParent && currentParent._id === id) {
        setCurrentParent({ ...currentParent, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update parent with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeParent = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteParent(id);
      setParents(parents.filter((parent) => parent._id !== id));
      if (currentParent && currentParent._id === id) {
        setCurrentParent(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete parent with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateStatus = async (id, statusInfo) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await updateParentStatus(id, statusInfo.status);
      setParents(
        parents.map((parent) =>
          parent._id === id ? { ...parent, status: data.data.status } : parent
        )
      );
      if (currentParent && currentParent._id === id) {
        setCurrentParent({ ...currentParent, status: data.data.status });
      }
      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to update status for parent with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <ParentContext.Provider
      value={{
        parents,
        setParents,
        currentParent,
        isLoading,
        error,
        addParent,
        fetchAllParents,
        fetchParentById,
        editParent,
        removeParent,
        updateStatus,
        parentOptions,
      }}
    >
      {children}
    </ParentContext.Provider>
  );
};

export const useParent = () => useContext(ParentContext);
