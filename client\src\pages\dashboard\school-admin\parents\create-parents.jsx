import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { ParentForm } from "@/components/forms/dashboard/parents/parent-form";
import { PageHeader } from "@/components/dashboard/page-header";
import { useNavigate, useParams } from "react-router-dom";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { useParent } from "@/context/parent-context";
import { toast } from "sonner";

const CreateParents = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchParentById, isLoading } = useParent();
  const [parentData, setParentData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadParentData = async () => {
      if (id) {
        try {
          const data = await fetchParentById(id);
          setParentData(data);
        } catch (error) {
          console.error("Failed to fetch parent data:", error);
          toast.error("Failed to load parent data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };

    loadParentData();
  }, [id, fetchParentById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit Parent" : "Create New Parent"}
        actions={[
          {
            label: "Back to Parents",
            href: "/dashboard/parents",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "Parents", href: "/dashboard/parents" },
          { label: id ? "Edit Parent" : "Create Parent" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <ParentForm editingId={id} initialData={parentData} />
      )}
    </Container>
  );
};

export default CreateParents;
