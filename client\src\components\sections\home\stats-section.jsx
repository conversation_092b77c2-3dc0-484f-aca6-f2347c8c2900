import { Section } from "@/components/ui/section";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";

export default function Stats({
  title = "Trusted by Educational Institutions Worldwide",
  subtitle = "Join thousands of schools that rely on School TRACK for their daily operations",
  badge = (
    <Announcement>
      <AnnouncementTag>STATS</AnnouncementTag>
      <AnnouncementTitle>Updated for 2023-2024 Academic Year</AnnouncementTitle>
    </Announcement>
  ),
  items = [
    {
      label: "over",
      value: 5000,
      description: "schools using our platform",
    },
    {
      label: "managing",
      value: 2.5,
      suffix: "M+",
      description: "student records worldwide",
    },
    {
      label: "supporting",
      value: 350,
      suffix: "K+",
      description: "teachers and administrators",
    },
    {
      label: "saving",
      value: 15,
      suffix: "hrs/week",
      description: "on administrative tasks",
    },
  ],
}) {
  return (
    <Section className="group relative overflow-hidden">
      <Container className="flex flex-col items-center gap-8 text-center">
        {badge !== false && badge}
        <h2 className="max-w-[640px] text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
          {title}
          {subtitle && (
            <p className="mt-4 text-lg leading-relaxed text-muted-foreground sm:text-xl">
              {subtitle}
            </p>
          )}
        </h2>
        {items !== false && items.length > 0 && (
          <div className="grid grid-cols-2 gap-12 sm:grid-cols-4">
            {items.map((item, index) => (
              <div
                key={index}
                className="flex flex-col items-start gap-3 text-left"
              >
                {item.label && (
                  <div className="text-muted-foreground text-sm font-semibold">
                    {item.label}
                  </div>
                )}
                <div className="flex items-baseline gap-2">
                  <div className="from-foreground to-foreground dark:to-brand bg-linear-to-r bg-clip-text text-4xl font-medium text-transparent drop-shadow-[2px_1px_24px_var(--brand-foreground)] transition-all duration-300 sm:text-5xl md:text-6xl">
                    {item.value}
                  </div>
                  {item.suffix && (
                    <div className="text-brand text-2xl font-semibold">
                      {item.suffix}
                    </div>
                  )}
                </div>
                {item.description && (
                  <div className="text-muted-foreground text-sm font-semibold text-pretty">
                    {item.description}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}
