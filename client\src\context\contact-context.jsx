import { createContext, useContext, useState, useEffect } from "react";
import {
  submitContactForm,
  getAllContactSubmissions,
  getContactSubmissionById,
  updateContactSubmissionStatus,
} from "@/api/contact-api";

const ContactContext = createContext({
  contacts: [],
  currentContact: null,
  isLoading: false,
  error: null,
  submitForm: () => {},
  fetchAllContacts: () => {},
  fetchContactById: () => {},
  updateContactStatus: () => {},
});

export const ContactProvider = ({ children }) => {
  const [contacts, setContacts] = useState([]);
  const [currentContact, setCurrentContact] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const submitForm = async (formData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await submitContactForm(formData);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to submit contact form");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllContacts = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getAllContactSubmissions();
      setContacts(response.data || []);
      setIsLoading(false);
      return response.contacts;
    } catch (error) {
      setError(error.message || "Failed to fetch contacts");
      setContacts([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchContactById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getContactSubmissionById(id);
      setCurrentContact(data.contact);
      setIsLoading(false);
      return data.contact;
    } catch (error) {
      setError(error.message || `Failed to fetch contact with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const updateContactStatus = async (id, statusUpdate) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await updateContactSubmissionStatus(id, statusUpdate);

      if (contacts.length > 0) {
        setContacts(
          contacts.map((contact) =>
            contact._id === id
              ? { ...contact, status: statusUpdate.status }
              : contact
          )
        );
      }

      if (currentContact && currentContact._id === id) {
        setCurrentContact({ ...currentContact, status: statusUpdate.status });
      }

      setIsLoading(false);
      return data;
    } catch (error) {
      setError(
        error.message || `Failed to update status for contact with ID: ${id}`
      );
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <ContactContext.Provider
      value={{
        contacts,
        currentContact,
        isLoading,
        error,
        submitForm,
        fetchAllContacts,
        fetchContactById,
        updateContactStatus,
      }}
    >
      {children}
    </ContactContext.Provider>
  );
};

export const useContact = () => useContext(ContactContext);
