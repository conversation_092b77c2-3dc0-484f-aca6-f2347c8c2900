import { createContext, useContext, useState } from "react";
import {
  createSchoolAdmin,
  getSchoolAdmins,
  getSchoolAdminById,
  updateSchoolAdmin,
  deleteSchoolAdmin,
} from "@/api/school-admin-api";

const SchoolAdminContext = createContext({
  schoolAdmins: [],
  currentSchoolAdmin: null,
  isLoading: false,
  error: null,
  addSchoolAdmin: () => {},
  fetchAllSchoolAdmins: () => {},
  fetchSchoolAdminById: () => {},
  editSchoolAdmin: () => {},
  removeSchoolAdmin: () => {},
});

export const SchoolAdminProvider = ({ children }) => {
  const [schoolAdmins, setSchoolAdmins] = useState([]);
  const [currentSchoolAdmin, setCurrentSchoolAdmin] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const addSchoolAdmin = async (adminData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await createSchoolAdmin(adminData);
      setSchoolAdmins((prevAdmins) => [...prevAdmins, response.data]);
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || "Failed to create school admin");
      setIsLoading(false);
      throw error;
    }
  };

  const fetchAllSchoolAdmins = async (schoolId = null) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSchoolAdmins(schoolId);
      setSchoolAdmins(response.data || []);
      setIsLoading(false);
      return response.data;
    } catch (error) {
      setError(error.message || "Failed to fetch school admins");
      setSchoolAdmins([]);
      setIsLoading(false);
      throw error;
    }
  };

  const fetchSchoolAdminById = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await getSchoolAdminById(id);
      const adminData = response.data;
      setCurrentSchoolAdmin(adminData);
      setIsLoading(false);
      return adminData;
    } catch (error) {
      setError(error.message || `Failed to fetch school admin with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const editSchoolAdmin = async (id, adminData) => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await updateSchoolAdmin(id, adminData);
      setSchoolAdmins(
        schoolAdmins.map((admin) =>
          admin._id === id ? { ...admin, ...response.data } : admin
        )
      );
      if (currentSchoolAdmin && currentSchoolAdmin._id === id) {
        setCurrentSchoolAdmin({ ...currentSchoolAdmin, ...response.data });
      }
      setIsLoading(false);
      return response;
    } catch (error) {
      setError(error.message || `Failed to update school admin with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  const removeSchoolAdmin = async (id) => {
    setIsLoading(true);
    setError(null);
    try {
      await deleteSchoolAdmin(id);
      setSchoolAdmins(schoolAdmins.filter((admin) => admin._id !== id));
      if (currentSchoolAdmin && currentSchoolAdmin._id === id) {
        setCurrentSchoolAdmin(null);
      }
      setIsLoading(false);
    } catch (error) {
      setError(error.message || `Failed to delete school admin with ID: ${id}`);
      setIsLoading(false);
      throw error;
    }
  };

  return (
    <SchoolAdminContext.Provider
      value={{
        schoolAdmins,
        currentSchoolAdmin,
        isLoading,
        error,
        addSchoolAdmin,
        fetchAllSchoolAdmins,
        fetchSchoolAdminById,
        editSchoolAdmin,
        removeSchoolAdmin,
      }}
    >
      {children}
    </SchoolAdminContext.Provider>
  );
};

export const useSchoolAdmin = () => useContext(SchoolAdminContext);
