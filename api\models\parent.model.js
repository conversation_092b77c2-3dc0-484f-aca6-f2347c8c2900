import mongoose from "mongoose";

const ParentSchema = new mongoose.Schema(
  {
    // Personal Information
    title: {
      type: String,
      required: [true, "Title is required"],
      trim: true,
    },
    firstName: {
      type: String,
      required: [true, "First name is required"],
      trim: true,
    },
    lastName: {
      type: String,
      required: [true, "Last name is required"],
      trim: true,
    },
    gender: {
      type: String,
      required: [true, "Gender is required"],
      trim: true,
    },
    dateOfBirth: {
      type: Date,
      required: [true, "Date of birth is required"],
    },
    bloodGroup: {
      type: String,
      trim: true,
    },
    maritalStatus: {
      type: String,
      trim: true,
    },
    nationality: {
      type: String,
      required: [true, "Nationality is required"],
      trim: true,
    },
    religion: {
      type: String,
      trim: true,
    },
    aadharNumber: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^\d{12}$/.test(v);
        },
        message: "Aadhar number must be 12 digits",
      },
    },
    panNumber: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^[A-Z]{5}\d{4}[A-Z]{1}$/.test(v);
        },
        message: "Invalid PAN number format",
      },
    },
    primaryParent: {
      type: Boolean,
      default: true,
    },
    profilePhoto: {
      type: String,
      trim: true,
    },

    // Contact Information
    personalEmail: {
      type: String,
      required: [true, "Personal email is required"],
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    phone: {
      type: String,
      required: [true, "Phone number is required"],
      trim: true,
    },
    alternatePhone: {
      type: String,
      trim: true,
    },
    preferredContactMethod: {
      type: String,
      enum: ["email", "phone"],
      default: "email",
    },

    // Address Information
    address: {
      type: String,
      required: [true, "Address is required"],
      trim: true,
    },
    city: {
      type: String,
      required: [true, "City is required"],
      trim: true,
    },
    state: {
      type: String,
      required: [true, "State is required"],
      trim: true,
    },
    pincode: {
      type: String,
      required: [true, "Pincode is required"],
      trim: true,
      validate: {
        validator: function (v) {
          return /^\d{6}$/.test(v);
        },
        message: "Pincode must be 6 digits",
      },
    },
    country: {
      type: String,
      required: [true, "Country is required"],
      trim: true,
    },

    // Professional Information
    occupation: {
      type: String,
      required: [true, "Occupation is required"],
      trim: true,
    },
    companyName: {
      type: String,
      trim: true,
    },
    designation: {
      type: String,
      trim: true,
    },
    workAddress: {
      type: String,
      trim: true,
    },
    workPhone: {
      type: String,
      trim: true,
    },
    qualification: {
      type: String,
      trim: true,
    },
    annualIncome: {
      type: String,
      trim: true,
    },

    // System Access & Permissions
    email: {
      type: String,
      required: [true, "System email is required"],
      unique: true,
      trim: true,
      match: [
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "Please provide a valid email",
      ],
    },
    password: {
      type: String,
      required: [true, "Password is required"],
      minlength: [8, "Password must be at least 8 characters"],
      select: false,
    },
    isActive: {
      type: Boolean,
      default: true,
    },

    // Additional Information
    bankName: {
      type: String,
      trim: true,
    },
    bankAccountNumber: {
      type: String,
      trim: true,
    },
    ifscCode: {
      type: String,
      trim: true,
      validate: {
        validator: function (v) {
          return !v || /^[A-Z]{4}0[A-Z0-9]{6}$/.test(v);
        },
        message: "Invalid IFSC code format",
      },
    },
    notes: {
      type: String,
      trim: true,
    },

    // Relationships
    children: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "Student",
      },
    ],

    // Status
    status: {
      type: String,
      enum: ["active", "inactive"],
      default: "active",
    },
  },
  {
    timestamps: true,
  }
);

export default mongoose.model("Parent", ParentSchema);
