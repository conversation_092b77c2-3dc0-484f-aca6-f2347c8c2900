import React from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { BookO<PERSON>, Users, ClipboardCheck, Clock } from "lucide-react";
import { WelcomeBanner } from "@/components/dashboard/welcome-banner";
import { StatCard } from "@/components/dashboard/stat-card";
import { useAuth } from "@/context/auth-context";

const TeacherOverview = () => {
  const { user, isLoading } = useAuth();

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 md:p-8">
      <WelcomeBanner
        user={user}
        isLoading={isLoading}
        title="Teacher Dashboard"
        subtitle="Manage your classes, assignments, and student progress"
      />

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="My Classes"
          value="5"
          description="Active classes this semester"
          icon={BookOpen}
          isLoading={isLoading}
        />

        <StatCard
          title="Total Students"
          value="142"
          description="Across all classes"
          icon={Users}
          isLoading={isLoading}
        />

        <StatCard
          title="Pending Assignments"
          value="24"
          description="Needs grading"
          icon={ClipboardCheck}
          isLoading={isLoading}
        />

        <StatCard
          title="Next Class"
          value="1:30 PM"
          description="Mathematics - Room 204"
          icon={Clock}
          isLoading={isLoading}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Class Performance</CardTitle>
            <CardDescription>Average grades by class</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[300px] bg-muted/20 rounded-md flex items-center justify-center">
              Performance Chart
            </div>
          </CardContent>
        </Card>

        <Card className="col-span-3">
          <CardHeader>
            <CardTitle>Upcoming Schedule</CardTitle>
            <CardDescription>Classes for the next 7 days</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Mathematics</p>
                  <p className="text-xs text-muted-foreground">
                    Today, 1:30 PM - Room 204
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Physics</p>
                  <p className="text-xs text-muted-foreground">
                    Today, 3:00 PM - Room 301
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Chemistry</p>
                  <p className="text-xs text-muted-foreground">
                    Tomorrow, 10:00 AM - Lab 2
                  </p>
                </div>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                <div className="flex-1">
                  <p className="text-sm font-medium">Biology</p>
                  <p className="text-xs text-muted-foreground">
                    Tomorrow, 2:00 PM - Lab 1
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default TeacherOverview;
