import axiosInstance from "./axios-instance";

export const createNotification = async (notificationData) => {
  try {
    const response = await axiosInstance.post(
      "/notifications/create",
      notificationData
    );
    return response.data;
  } catch (error) {
    console.error(
      "Error creating notification:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to create notification");
  }
};

export const getNotifications = async (filters = {}) => {
  try {
    let queryParams = new URLSearchParams();

    // Add filters to query params
    if (filters.schoolId) queryParams.append("schoolId", filters.schoolId);
    if (filters.type) queryParams.append("type", filters.type);
    if (filters.status) queryParams.append("status", filters.status);
    if (filters.priority) queryParams.append("priority", filters.priority);

    const url = `/notifications?${queryParams.toString()}`;
    const response = await axiosInstance.get(url);
    return response.data;
  } catch (error) {
    console.error(
      "Error fetching notifications:",
      error.response?.data || error.message
    );
    throw error.response?.data || new Error("Failed to fetch notifications");
  }
};

export const getNotificationById = async (id) => {
  try {
    const response = await axiosInstance.get(`/notifications/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error fetching notification with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to fetch notification with ID ${id}`)
    );
  }
};

export const updateNotification = async (id, notificationData) => {
  try {
    const response = await axiosInstance.put(
      `/notifications/${id}`,
      notificationData
    );
    return response.data;
  } catch (error) {
    console.error(
      `Error updating notification with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to update notification with ID ${id}`)
    );
  }
};

export const deleteNotification = async (id) => {
  try {
    const response = await axiosInstance.delete(`/notifications/${id}`);
    return response.data;
  } catch (error) {
    console.error(
      `Error deleting notification with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to delete notification with ID ${id}`)
    );
  }
};

export const sendNotification = async (id) => {
  try {
    const response = await axiosInstance.post(`/notifications/${id}/send`);
    return response.data;
  } catch (error) {
    console.error(
      `Error sending notification with ID ${id}:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to send notification with ID ${id}`)
    );
  }
};

export const markNotificationAsRead = async (id) => {
  try {
    const response = await axiosInstance.post(`/notifications/${id}/read`);
    return response.data;
  } catch (error) {
    console.error(
      `Error marking notification with ID ${id} as read:`,
      error.response?.data || error.message
    );
    throw (
      error.response?.data ||
      new Error(`Failed to mark notification with ID ${id} as read`)
    );
  }
};
