import React, { useEffect, useState } from "react";
import { Container } from "@/components/ui/container";
import { useNavigate, useParams } from "react-router-dom";
import { PageHeader } from "@/components/dashboard/page-header";
import { FormCardSkeleton } from "@/components/forms/form-card-skeleton";
import { SchoolAdminForm } from "@/components/forms/dashboard/school-admin/school-admin-form";
import { useSchoolAdmin } from "@/context/school-admin-context";
import { toast } from "sonner";

const CreateSchoolAdmin = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { fetchSchoolAdminById } = useSchoolAdmin();
  const [schoolAdminData, setSchoolAdminData] = useState(null);
  const [loading, setLoading] = useState(!!id);

  useEffect(() => {
    const loadAdminData = async () => {
      if (id) {
        try {
          const data = await fetchSchoolAdminById(id);
          setSchoolAdminData(data);
        } catch (error) {
          console.error("Failed to fetch school admin data:", error);
          toast.error("Failed to load school admin data");
        } finally {
          setLoading(false);
        }
      } else {
        setLoading(false);
      }
    };
    loadAdminData();
  }, [id, fetchSchoolAdminById, navigate]);

  return (
    <Container className="py-8">
      <PageHeader
        title={id ? "Edit School Admin" : "Create New School Admin"}
        actions={[
          {
            label: "Back to School Admins",
            href: "/dashboard/school-admins",
          },
        ]}
        breadcrumbs={[
          { label: "Dashboard", href: "/dashboard" },
          { label: "School Admins", href: "/dashboard/school-admins" },
          { label: id ? "Edit School Admin" : "Create School Admin" },
        ]}
      />

      {loading ? (
        <FormCardSkeleton />
      ) : (
        <SchoolAdminForm editingId={id} initialData={schoolAdminData} />
      )}
    </Container>
  );
};

export default CreateSchoolAdmin;
