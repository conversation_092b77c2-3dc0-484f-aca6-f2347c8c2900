import React from "react";
import { CloseButton } from "@/components/form-inputs/close-button";
import { SubmitButton } from "@/components/form-inputs/submit-button";
import { Card, CardContent } from "@/components/ui/card";

export function FormHeader({ title, editingId, loading, href, parent }) {
  return (
    <div className="*:data-[slot=card]:from-primary/5 *:data-[slot=card]:to-card dark:*:data-[slot=card]:bg-card *:data-[slot=card]:bg-gradient-to-t *:data-[slot=card]:shadow-xs">
      <Card className="mb-6">
        <CardContent className="px-4 flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-2xl font-bold tracking-tight">
              {editingId ? "Update" : "Create"} {title}
            </h1>
          </div>
          <div className="flex items-center justify-center gap-2">
            <div className="hidden md:block">
              <CloseButton href={href} parent={parent} />
            </div>
            <SubmitButton
              size={"sm"}
              title={editingId ? `Update ${title}` : `Save ${title}`}
              loading={loading}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
