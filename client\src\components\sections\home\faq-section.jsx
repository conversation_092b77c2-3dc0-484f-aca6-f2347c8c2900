import React from "react";
import { cn } from "@/lib/utils";
import { Section } from "@/components/ui/section";
import {
  Announcement,
  AnnouncementTag,
  AnnouncementTitle,
} from "@/components/ui/announcement";
import { Container } from "@/components/ui/container";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { HelpCircle } from "lucide-react";

export default function FAQ({
  title = "Frequently Asked Questions",
  subtitle = "Find answers to common questions about School TRACK",
  badge = (
    <Announcement>
      <AnnouncementTag>FAQ</AnnouncementTag>
      <AnnouncementTitle>Everything you need to know</AnnouncementTitle>
    </Announcement>
  ),
  faqs = [
    {
      question: "How long does implementation typically take?",
      answer:
        "The standard implementation timeline is 6-8 weeks, but this can vary based on your institution's size and specific requirements. Our team works closely with you to ensure a smooth transition with minimal disruption to your operations.",
    },
    {
      question: "Is my data secure with School TRACK?",
      answer:
        "Absolutely. We employ industry-leading security measures including end-to-end encryption, regular security audits, and compliance with educational data privacy regulations. Your data is stored in secure, redundant servers with strict access controls.",
    },
    {
      question: "Can School TRACK integrate with our existing systems?",
      answer:
        "Yes, School TRACK is designed with integration in mind. We offer APIs and pre-built connectors for popular educational tools, student information systems, and financial software. Our team can work with you to ensure seamless data flow between systems.",
    },
    {
      question: "What kind of training and support do you provide?",
      answer:
        "We provide comprehensive training for all user types including administrators, teachers, and support staff. This includes live training sessions, on-demand video tutorials, and detailed documentation. Our support team is available via chat, email, and phone during business hours, with emergency support available 24/7.",
    },
    {
      question: "Can parents access the system?",
      answer:
        "Yes, School TRACK includes a dedicated parent portal where parents can view their children's attendance, grades, assignments, and communicate with teachers. The portal is available via web and mobile app for convenient access.",
    },
    {
      question:
        "Is School TRACK suitable for all types of educational institutions?",
      answer:
        "School TRACK is designed to be flexible and can be configured for various types of institutions including K-12 schools, colleges, universities, and specialized educational centers. Our different pricing tiers accommodate institutions of all sizes.",
    },
  ],
  className,
}) {
  return (
    <Section className={cn("group relative", className)}>
      <Container className="flex flex-col items-center gap-12">
        <div className="flex flex-col items-center gap-4 text-center">
          {badge !== false && badge}
          {title && (
            <h2 className="text-3xl leading-tight font-semibold sm:text-5xl sm:leading-tight">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-md text-muted-foreground max-w-[600px] font-medium sm:text-xl">
              {subtitle}
            </p>
          )}
        </div>

        {faqs && faqs.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
            {faqs.map((faq, index) => (
              <Card key={index} className="border-border/50">
                <CardHeader className="pb-2">
                  <CardTitle className="flex items-start gap-2 text-lg">
                    <HelpCircle className="size-5 text-primary mt-0.5 shrink-0" />
                    <span>{faq.question}</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-foreground/80 text-sm">
                    {faq.answer}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </Container>
    </Section>
  );
}
