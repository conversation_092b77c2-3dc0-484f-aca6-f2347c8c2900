import React from "react";
import { Button } from "@/components/ui/button";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Skeleton } from "@/components/ui/skeleton";

export function PageHeader({
  title,
  isLoading = false,
  breadcrumbs = [],
  actions = [],
  className = "",
}) {
  return (
    <div
      className={`flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 ${className}`}
    >
      <div className="flex flex-col space-y-2 min-w-0 flex-1">
        {isLoading ? (
          <>
            <Skeleton className="h-6 sm:h-8 w-32 sm:w-48" />
            {breadcrumbs.length > 0 && (
              <div className="flex flex-wrap gap-1 sm:gap-2">
                {Array(breadcrumbs.length)
                  .fill(0)
                  .map((_, index) => (
                    <div key={index} className="flex items-center gap-1">
                      <Skeleton className="h-4 sm:h-5 w-16 sm:w-20" />
                      {index < breadcrumbs.length - 1 && (
                        <Skeleton className="h-4 sm:h-5 w-3 sm:w-4" />
                      )}
                    </div>
                  ))}
              </div>
            )}
          </>
        ) : (
          <>
            <h2 className="text-xl sm:text-2xl font-bold tracking-tight truncate pr-2">
              {title}
            </h2>
            {breadcrumbs.length > 0 && (
              <div className="text-muted-foreground text-sm">
                <Breadcrumb>
                  <BreadcrumbList className="flex-wrap">
                    {breadcrumbs.map((breadcrumb, index) => (
                      <React.Fragment key={index}>
                        <BreadcrumbItem className="text-xs sm:text-sm">
                          {breadcrumb.href ? (
                            <BreadcrumbLink
                              href={breadcrumb.href}
                              className="truncate max-w-24 sm:max-w-none"
                            >
                              {breadcrumb.label}
                            </BreadcrumbLink>
                          ) : (
                            <BreadcrumbPage className="truncate max-w-24 sm:max-w-none">
                              {breadcrumb.label}
                            </BreadcrumbPage>
                          )}
                        </BreadcrumbItem>
                        {index < breadcrumbs.length - 1 && (
                          <BreadcrumbSeparator className="text-xs sm:text-sm" />
                        )}
                      </React.Fragment>
                    ))}
                  </BreadcrumbList>
                </Breadcrumb>
              </div>
            )}
          </>
        )}
      </div>
      {actions.length > 0 && (
        <div className="flex items-center gap-2 flex-wrap sm:flex-nowrap">
          {isLoading
            ? Array(actions.length)
                .fill(0)
                .map((_, index) => (
                  <Skeleton key={index} className="h-8 sm:h-9 w-20 sm:w-24" />
                ))
            : actions.map((action, index) => (
                <Button
                  variant={action.variant || "default"}
                  key={index}
                  onClick={action.onClick}
                  className={`text-xs sm:text-sm h-8 sm:h-9 px-2 sm:px-3 ${
                    action.className || ""
                  }`}
                  size="sm"
                >
                  {action.icon && (
                    <action.icon className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-1 sm:mr-2" />
                  )}
                  {action.href ? (
                    <a href={action.href} className="truncate">
                      {action.label}
                    </a>
                  ) : (
                    <span className="truncate">{action.label}</span>
                  )}
                </Button>
              ))}
        </div>
      )}
    </div>
  );
}
